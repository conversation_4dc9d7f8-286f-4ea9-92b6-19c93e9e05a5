[workspace]
resolver = "2"
members = [
    "prisma_ai"
]

# Workspace-wide settings
[workspace.package]
version = "0.1.0"
edition = "2021"

# Shared dependencies for all workspace members
[workspace.dependencies]
# Async runtime
num_cpus = "1.16.0"
rayon = "1.10.0"
sysinfo = "0.30.13"
tokio = { version = "1.36.0", features = ["full"] }
async-trait = "0.1.77"
tokio-executor-trait = "2.1.0"  # Add this line for lapin executor support

# Error handling
anyhow = "1.0.80"
thiserror = "1.0.57"

# Logging and metrics
tracing = { version = "0.1.40", features = ["attributes"] }
tracing-subscriber = { version = "0.3.18", features = ["env-filter"] }
tracing-loki = { version = "0.2.6", default-features = false, features = ["rustls", "compat-0-2-1"] }

# Serialization
serde = { version = "1.0.197" }
serde_derive = "1.0.197"
serde_json = "1.0.114"

# Time handling
chrono = { version = "0.4.34", features = ["serde", "clock"] }

# Authentication dependencies
jsonwebtoken = "9.2.0"
bcrypt = "0.15.0"
base64 = "0.22.0"

# Redis dependencies
redis = { version = "0.27.5", features = ["tokio-comp", "connection-manager", "json"] }

# Core dependencies
opentelemetry = { version = "0.28.0", features = ["trace"] }
opentelemetry_sdk = { version = "0.28.0", features = ["trace", "rt-tokio"] }
opentelemetry-otlp = { version = "0.28.0", features = ["trace", "grpc-tonic", "tonic"] }
opentelemetry-semantic-conventions = { version = "0.28.0" }
lapin = "3.1.0"
futures = "0.3"
futures-util = "0.3"
surrealdb = { version = "2.3.7", features = ["kv-rocksdb", "protocol-ws", "protocol-http"] }
bindgen = "0.69"

# Build dependencies
proc-macro2 = "1.0"
quote = "1.0"
syn = { version = "2.0", features = ["full"] }

# Proto dependencies
prost = { version = "0.13.5", features = ["prost-derive"] }
prost-types = { version = "0.13.5" }

[workspace.metadata]
# Force all dependencies to use specific versions
dependencies.prost = "=0.13.5"
dependencies.prost-types = "=0.13.5"