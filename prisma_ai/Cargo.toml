[package]
name = "prisma_ai"
version = "0.1.0"
edition = "2021"

[dependencies]
# Proto dependencies
prost = { workspace = true }
prost-types = { workspace = true }

# Async runtime and utilities
tokio = { workspace = true }
futures = { workspace = true }
futures-util = { workspace = true }

# Error handling
anyhow = { workspace = true }
thiserror = { workspace = true }

# Logging and tracing
tracing = { workspace = true }
tracing-subscriber = { workspace = true }
tracing-loki = { workspace = true }
opentelemetry = { workspace = true }
opentelemetry_sdk = { workspace = true }
opentelemetry-otlp = { workspace = true }
opentelemetry-prometheus = "0.14.0"
opentelemetry-semantic-conventions = { workspace = true }
prometheus = { version = "0.13.4", features = ["process"] }
lazy_static = "1.4"

# Serialization
serde = { workspace = true }
serde_derive = { workspace = true }
serde_json = { workspace = true }
serde_yaml = "0.9.32" # Added for YAML configuration
chrono = { workspace = true }
toml = "0.8.10"
tera = "1.19" # Added templating engine

# Database
surrealdb = { workspace = true }
uuid = { version = "1.0", features = ["v4", "serde"] }

# Authentication
jsonwebtoken = { workspace = true }
bcrypt = { workspace = true }
base64 = { workspace = true }

# Redis caching
redis = { workspace = true }

# Message queue
lapin = { workspace = true }
tokio-executor-trait = { workspace = true }  # Use the workspace dependency

# Bitflags for token attributes
bitflags = "2.4"

# Build dependencies
cc = "1.0"
bindgen = { workspace = true }

# Core dependencies
async-trait = { workspace = true }
rand = "0.8.5" # Added for random number generation

# Build dependencies
proc-macro2 = { workspace = true }
quote = { workspace = true }
syn = { workspace = true }

# HTTP client
reqwest = { version = "0.11", features = ["json"] }
url = "2.5"

log = "0.4"
tempfile = "3.10"
rayon = { workspace = true } # Added for parallel execution
num_cpus = { workspace = true } # Added for system info
sysinfo = { workspace = true } # Added for system monitoring

# MCP (Model Context Protocol) dependencies
rmcp = { version = "0.2.0", features = ["client", "server", "transport-child-process", "transport-io", "reqwest", "macros"] }

[build-dependencies]
bindgen = { workspace = true }
cc = "1.0"

[features]
default = []
test_mocks = [] # Feature flag for enabling test mocks

[dev-dependencies]
tokio-test = "0.4"
test-log = "0.2"
env_logger = "0.10"
pretty_assertions = "1.4"
chrono = { workspace = true }
serde = { workspace = true }
serde_json = { workspace = true }
async-trait = { workspace = true }

[[test]]
name = "integration_tests"
path = "tests/integration_tests.rs"

[[example]]
name = "real_telemetry_deployment"
path = "examples/real_telemetry_deployment.rs"
