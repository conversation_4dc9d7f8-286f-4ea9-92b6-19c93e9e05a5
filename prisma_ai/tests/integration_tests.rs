mod common;
mod integration {
    mod agent_manager_tests;
    // Uncomment these as they are implemented
    mod decision_maker_tests;
    mod execution_strategies_tests;
    mod executor_tests;
    mod health_service_tests; // Health Service Integration Tests
    mod health_alerts_test; // Health Alert System Integration Tests
    mod health_dashboard_test; // Health Dashboard Generation Integration Tests
    mod end_to_end_integration_test; // Complete End-to-End Integration Tests
    mod monitoring_stack_integration_test; // Monitoring Stack Integration Tests
    mod cross_service_integration_test; // Cross-Service Integration Tests
    mod mcp_tests; // MCP (Model Context Protocol) Integration Tests
    mod monitor_tests;
    mod monitor_tests_v2;  // Memory monitor tests with clean imports
    mod monitor_tests_v3;  // Task status management tests
    mod monitor_tests_v4; // Task Monitor Integration Tests (v4)
    mod tcl_tests; // TCL (Task Creation Layer) Integration Tests
    mod prisma_engine_tests; // Prisma Engine Integration Tests
    mod telemetry_loki_test; // Telemetry Loki Integration Tests
    mod core_services_tests; // Core UI Services Integration Tests (Auth, Session, Cache, Health)
}
