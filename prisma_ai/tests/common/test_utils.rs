use std::sync::Arc;
use prisma_ai::storage::Database;
use prisma_ai::err::PrismaResult;

/// Test configuration structure
#[derive(Debug, <PERSON>lone)]
pub struct TestConfig {
    pub database_url: String,
    pub test_namespace: String,
    pub test_database_name: String,
}

impl Default for TestConfig {
    fn default() -> Self {
        Self {
            database_url: "http://localhost:8000".to_string(), // Use HTTP as configured in settings.toml
            test_namespace: "test_prisma".to_string(),
            test_database_name: "test_db".to_string(),
        }
    }
}

/// Setup test database for integration tests using real SurrealDB
pub async fn setup_test_database() -> PrismaResult<Arc<Database>> {
    // Create a real database connection to SurrealDB running in Docker
    // Use the default configuration which connects to localhost:8000
    let database = Database::new_with_defaults().await?;

    Ok(Arc::new(database))
}

/// Cleanup test database after tests
pub async fn cleanup_test_database() -> PrismaResult<()> {
    // For integration tests, we might want to clean up test data
    // For now, we'll leave the test data for debugging purposes
    Ok(())
}