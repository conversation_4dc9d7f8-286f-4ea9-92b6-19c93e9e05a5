// =================================================================================================
// File: /prisma_ai/src/prisma_ui/tests.rs
// =================================================================================================
// Purpose: Comprehensive unit tests for core UI foundation components (types, traits, generics).
// This file contains tests for the foundational types, traits, and generic utilities that all
// other UI components depend on, ensuring they work correctly with real implementations.
// =================================================================================================

#[cfg(test)]
mod tests {
    use crate::prisma_ui::types::*;
    use crate::prisma_ui::traits::*;
    use crate::prisma_ui::generics::*;
    use chrono::{DateTime, Utc};
    use std::collections::HashMap;
    use std::sync::Arc;
    use serde::{Serialize, Deserialize};

    // =================================================================================================
    // Type Tests
    // =================================================================================================

    #[test]
    fn test_user_session_creation() {
        let device_info = DeviceInfo {
            device_id: "test_device_123".to_string(),
            device_type: DeviceType::Web,
            platform: "Chrome".to_string(),
            app_version: "1.0.0".to_string(),
            user_agent: Some("Mozilla/5.0".to_string()),
        };

        let session = UserSession {
            session_id: "sess_123".to_string(),
            user_id: "user_456".to_string(),
            state: SessionState::Active,
            created_at: Utc::now(),
            last_activity: Utc::now(),
            expires_at: Some(Utc::now() + chrono::Duration::hours(24)),
            device_info,
            permissions: vec![Permission::ChatAccess, Permission::AgentInteraction],
        };

        assert_eq!(session.session_id, "sess_123");
        assert_eq!(session.user_id, "user_456");
        assert_eq!(session.state, SessionState::Active);
        assert_eq!(session.permissions.len(), 2);
        assert!(session.permissions.contains(&Permission::ChatAccess));
    }

    #[test]
    fn test_ui_request_response_creation() {
        let request = UiRequest {
            request_id: "req_123".to_string(),
            session_id: "sess_456".to_string(),
            timestamp: Utc::now(),
            request_type: "chat_message".to_string(),
            payload: "Hello, world!".to_string(),
        };

        assert_eq!(request.request_id, "req_123");
        assert_eq!(request.request_type, "chat_message");
        assert_eq!(request.payload, "Hello, world!");

        let response = UiResponse::success("req_123", "Response data");
        assert_eq!(response.request_id, "req_123");
        assert_eq!(response.status, ResponseStatus::Success);
        assert!(response.payload.is_some());
        assert!(response.error.is_none());

        let error = UiError::new("INVALID_INPUT", "Invalid input provided")
            .with_user_message("Please check your input and try again")
            .with_retry_after(30);
        
        let error_response = UiResponse::<String>::error("req_456", error);
        assert_eq!(error_response.status, ResponseStatus::Error);
        assert!(error_response.payload.is_none());
        assert!(error_response.error.is_some());
        
        let error_data = error_response.error.unwrap();
        assert_eq!(error_data.code, "INVALID_INPUT");
        assert_eq!(error_data.retry_after, Some(30));
    }

    #[test]
    fn test_websocket_message_serialization() {
        let chat_data = ChatMessageData {
            conversation_id: "conv_123".to_string(),
            message_id: "msg_456".to_string(),
            sender_id: "user_789".to_string(),
            sender_type: SenderType::User,
            content: "Test message".to_string(),
            timestamp: Utc::now(),
            metadata: None,
        };

        let message = WebSocketMessage::ChatMessage(chat_data);
        
        // Test serialization
        let serialized = serde_json::to_string(&message).expect("Should serialize");
        assert!(serialized.contains("ChatMessage"));
        assert!(serialized.contains("Test message"));

        // Test deserialization
        let deserialized: WebSocketMessage = serde_json::from_str(&serialized).expect("Should deserialize");
        match deserialized {
            WebSocketMessage::ChatMessage(data) => {
                assert_eq!(data.content, "Test message");
                assert_eq!(data.sender_type, SenderType::User);
            }
            _ => panic!("Expected ChatMessage variant"),
        }
    }

    #[test]
    fn test_configuration_types() {
        let server_config = ServerConfig {
            host: "localhost".to_string(),
            port: 8080,
            tls_enabled: false,
            cors_origins: vec!["http://localhost:3000".to_string()],
            max_connections: Some(1000),
        };

        let auth_config = AuthConfig {
            jwt_secret: "secret_key".to_string(),
            jwt_expiry_hours: 24,
            refresh_token_expiry_days: 30,
            password_min_length: 8,
            require_email_verification: true,
            max_login_attempts: 5,
            lockout_duration_minutes: 15,
        };

        let ui_config = UiConfig {
            server: server_config,
            rabbitmq: RabbitMqConfig {
                host: "localhost".to_string(),
                port: 5672,
                username: "guest".to_string(),
                password: "guest".to_string(),
                virtual_host: None,
                exchange: "ui_exchange".to_string(),
                queue_prefix: "ui_".to_string(),
                websocket_enabled: true,
                websocket_port: Some(15674),
            },
            database: DatabaseConfig {
                host: "localhost".to_string(),
                port: 8000,
                database: "prisma_ui".to_string(),
                username: Some("root".to_string()),
                password: Some("password".to_string()),
                connection_pool_size: Some(10),
            },
            auth: auth_config,
            features: FeatureConfig {
                chat_enabled: true,
                agent_management_enabled: true,
                project_creation_enabled: true,
                mcp_integration_enabled: false,
                telemetry_enabled: true,
                debug_mode: false,
            },
            ui_settings: UiSettings {
                theme: "dark".to_string(),
                language: "en".to_string(),
                timezone: "UTC".to_string(),
                date_format: "YYYY-MM-DD".to_string(),
                max_chat_history: 100,
                auto_save_interval_seconds: 30,
                notification_settings: NotificationSettings {
                    desktop_notifications: true,
                    sound_enabled: true,
                    email_notifications: false,
                    severity_filter: vec![NotificationSeverity::Warning, NotificationSeverity::Error],
                },
            },
        };

        assert_eq!(ui_config.server.host, "localhost");
        assert_eq!(ui_config.server.port, 8080);
        assert!(ui_config.features.chat_enabled);
        assert!(!ui_config.features.debug_mode);
        assert_eq!(ui_config.ui_settings.theme, "dark");
    }

    #[test]
    fn test_event_types() {
        let click_event = ClickEvent {
            element_id: "button_123".to_string(),
            element_type: "button".to_string(),
            coordinates: Some((100.0, 200.0)),
            modifiers: vec![KeyModifier::Ctrl],
        };

        let ui_event = UiEvent::Click(click_event);
        
        // Test serialization
        let serialized = serde_json::to_string(&ui_event).expect("Should serialize");
        assert!(serialized.contains("Click"));
        assert!(serialized.contains("button_123"));

        let navigation_event = NavigationEvent {
            from_route: "/home".to_string(),
            to_route: "/chat".to_string(),
            navigation_type: NavigationType::UserInitiated,
        };

        let nav_ui_event = UiEvent::Navigation(navigation_event);
        let nav_serialized = serde_json::to_string(&nav_ui_event).expect("Should serialize");
        assert!(nav_serialized.contains("Navigation"));
        assert!(nav_serialized.contains("/chat"));
    }

    #[test]
    fn test_integration_types() {
        let task_request = TaskSubmissionRequest {
            task_type: "llm_inference".to_string(),
            parameters: {
                let mut params = HashMap::new();
                params.insert("prompt".to_string(), serde_json::Value::String("Hello".to_string()));
                params.insert("max_tokens".to_string(), serde_json::Value::Number(serde_json::Number::from(100)));
                params
            },
            priority: TaskPriority::High,
            agent_id: Some("agent_123".to_string()),
            conversation_id: Some("conv_456".to_string()),
        };

        assert_eq!(task_request.task_type, "llm_inference");
        assert_eq!(task_request.priority, TaskPriority::High);
        assert!(task_request.agent_id.is_some());
        assert_eq!(task_request.parameters.len(), 2);

        let agent_config = AgentConfiguration {
            name: "Test Agent".to_string(),
            description: Some("A test agent".to_string()),
            role: "assistant".to_string(),
            goal: "Help users".to_string(),
            model_config: ModelConfiguration {
                model_path: "/path/to/model".to_string(),
                model_type: "llama".to_string(),
                context_length: Some(4096),
                embedding_model: Some("/path/to/embedding".to_string()),
            },
            prompt_template: Some("You are a helpful assistant".to_string()),
            max_tokens: Some(512),
            temperature: Some(0.7),
        };

        assert_eq!(agent_config.name, "Test Agent");
        assert_eq!(agent_config.role, "assistant");
        assert_eq!(agent_config.model_config.context_length, Some(4096));
    }

    // =================================================================================================
    // Default Implementation Tests
    // =================================================================================================

    #[test]
    fn test_default_implementations() {
        assert_eq!(UiState::default(), UiState::Loading);
        assert_eq!(SessionState::default(), SessionState::Initializing);
        assert_eq!(DeviceType::default(), DeviceType::Web);
        assert_eq!(ResponseStatus::default(), ResponseStatus::Success);
        assert_eq!(TaskPriority::default(), TaskPriority::Normal);
        assert_eq!(AgentStatus::default(), AgentStatus::Initializing);
        assert_eq!(TaskStatus::default(), TaskStatus::Queued);
        assert_eq!(ConnectionStatus::default(), ConnectionStatus::NotConfigured);
        assert_eq!(ServiceStatus::default(), ServiceStatus::NotInitialized);
        assert_eq!(ComponentStatus::default(), ComponentStatus::NotInitialized);
        assert_eq!(HealthStatus::default(), HealthStatus::Unknown);
    }

    // =================================================================================================
    // Generic Tests
    // =================================================================================================

    #[tokio::test]
    async fn test_generic_service() {
        struct TestService {
            value: i32,
        }

        let test_service = TestService { value: 42 };
        let generic_service = GenericService::new(test_service, "test_service");

        assert_eq!(generic_service.get_name(), "test_service");
        assert_eq!(generic_service.get_status().await, ServiceStatus::NotInitialized);

        // Test status updates
        generic_service.set_status(ServiceStatus::Running).await;
        assert_eq!(generic_service.get_status().await, ServiceStatus::Running);

        // Test configuration
        let config = create_test_ui_config();
        generic_service.set_config(config.clone()).await;
        let retrieved_config = generic_service.get_config().await;
        assert!(retrieved_config.is_some());
        assert_eq!(retrieved_config.unwrap().server.host, config.server.host);

        // Test inner service access
        {
            let inner = generic_service.read().await;
            assert_eq!(inner.value, 42);
        }

        {
            let mut inner = generic_service.write().await;
            inner.value = 100;
        }

        {
            let inner = generic_service.read().await;
            assert_eq!(inner.value, 100);
        }
    }

    #[tokio::test]
    async fn test_generic_session_manager() {
        struct MockStorage;
        let storage = Arc::new(MockStorage);
        let auth_config = create_test_auth_config();

        let session_manager = GenericSessionManager::new(storage, auth_config);

        // Test initial state
        assert_eq!(session_manager.active_sessions_count().await, 0);

        // Test adding sessions
        let session1 = create_test_user_session("sess_1", "user_1");
        let session2 = create_test_user_session("sess_2", "user_2");

        session_manager.add_active_session(session1.clone()).await;
        session_manager.add_active_session(session2.clone()).await;

        assert_eq!(session_manager.active_sessions_count().await, 2);

        let active_sessions = session_manager.get_active_sessions().await;
        assert_eq!(active_sessions.len(), 2);

        // Test removing sessions
        let removed = session_manager.remove_active_session(&session1.session_id).await;
        assert!(removed.is_some());
        assert_eq!(removed.unwrap().session_id, session1.session_id);
        assert_eq!(session_manager.active_sessions_count().await, 1);
    }

    #[tokio::test]
    async fn test_generic_event_publisher() {
        struct MockChannel {
            name: String,
        }

        let publisher = GenericEventPublisher::<MockChannel>::new(10);

        // Test adding channels
        let channel1 = Arc::new(MockChannel { name: "channel1".to_string() });
        let channel2 = Arc::new(MockChannel { name: "channel2".to_string() });

        publisher.add_channel("ch1", channel1.clone()).await;
        publisher.add_channel("ch2", channel2.clone()).await;

        // Test retrieving channels
        let retrieved = publisher.get_channel("ch1").await;
        assert!(retrieved.is_some());
        assert_eq!(retrieved.unwrap().name, "channel1");

        // Test event queuing
        let event = QueuedEvent {
            event: UiEvent::System(SystemEvent {
                event_id: "evt_1".to_string(),
                event_type: SystemEventType::Startup,
                description: "System started".to_string(),
                metadata: None,
            }),
            target: EventTarget::Broadcast,
            timestamp: Utc::now(),
            retry_count: 0,
            max_retries: 3,
        };

        publisher.queue_event(event).await.expect("Should queue event");
        assert_eq!(publisher.queued_events_count().await, 1);

        // Test draining events
        let drained = publisher.drain_queued_events().await;
        assert_eq!(drained.len(), 1);
        assert_eq!(publisher.queued_events_count().await, 0);

        // Test removing channels
        let removed = publisher.remove_channel("ch1").await;
        assert!(removed.is_some());
        assert_eq!(removed.unwrap().name, "channel1");
    }

    #[tokio::test]
    async fn test_generic_config_manager() {
        #[derive(Clone, Serialize, Deserialize, PartialEq, Debug)]
        struct TestConfig {
            value: i32,
            name: String,
        }

        let initial_config = TestConfig {
            value: 42,
            name: "initial".to_string(),
        };

        let config_manager = GenericConfigManager::new(initial_config.clone(), "/tmp/test_config.json");

        // Test getting initial config
        let current = config_manager.get_config().await;
        assert_eq!(current, initial_config);

        // Test updating config
        let new_config = TestConfig {
            value: 100,
            name: "updated".to_string(),
        };

        config_manager.update_config(new_config.clone()).await.expect("Should update config");
        let updated = config_manager.get_config().await;
        assert_eq!(updated, new_config);
        assert_eq!(updated.value, 100);
        assert_eq!(updated.name, "updated");

        // Test file path
        assert_eq!(config_manager.file_path(), "/tmp/test_config.json");
    }

    #[tokio::test]
    async fn test_config_section_manager() {
        #[derive(Clone, Debug, PartialEq)]
        struct SectionConfig {
            enabled: bool,
            value: String,
        }

        let mut section_manager = ConfigSectionManager::<SectionConfig>::new();

        // Test adding sections
        let section1 = SectionConfig {
            enabled: true,
            value: "section1".to_string(),
        };
        let section2 = SectionConfig {
            enabled: false,
            value: "section2".to_string(),
        };

        section_manager.add_section("sec1", section1.clone()).await;
        section_manager.add_section("sec2", section2.clone()).await;

        // Test retrieving sections
        let retrieved = section_manager.get_section("sec1").await;
        assert!(retrieved.is_some());
        assert_eq!(retrieved.unwrap(), section1);

        // Test listing sections
        let sections = section_manager.list_sections().await;
        assert_eq!(sections.len(), 2);
        assert!(sections.contains(&"sec1".to_string()));
        assert!(sections.contains(&"sec2".to_string()));

        // Test updating sections
        let updated_section = SectionConfig {
            enabled: false,
            value: "updated".to_string(),
        };
        let update_result = section_manager.update_section("sec1", updated_section.clone()).await;
        assert!(update_result);

        let retrieved_updated = section_manager.get_section("sec1").await;
        assert_eq!(retrieved_updated.unwrap(), updated_section);

        // Test default section
        section_manager.set_default_section("sec2");
        let default = section_manager.get_default_section().await;
        assert!(default.is_some());
        assert_eq!(default.unwrap(), section2);

        // Test removing sections
        let removed = section_manager.remove_section("sec1").await;
        assert!(removed.is_some());
        assert_eq!(removed.unwrap(), updated_section);
        assert_eq!(section_manager.list_sections().await.len(), 1);
    }

    #[tokio::test]
    async fn test_paginated_collection() {
        let items = vec!["item1", "item2", "item3"];
        let collection = PaginatedCollection::new(items.clone(), 10, 1, 3);

        assert_eq!(collection.items, items);
        assert_eq!(collection.total_count, 10);
        assert_eq!(collection.page, 1);
        assert_eq!(collection.page_size, 3);
        assert_eq!(collection.total_pages, 4);
        assert!(collection.has_next);
        assert!(!collection.has_previous);
        assert_eq!(collection.items_count(), 3);
        assert!(!collection.is_empty());

        // Test mapping
        let mapped = collection.map(|item| item.to_uppercase());
        assert_eq!(mapped.items, vec!["ITEM1", "ITEM2", "ITEM3"]);
        assert_eq!(mapped.total_count, 10);
    }

    #[tokio::test]
    async fn test_generic_cache() {
        let cache = GenericCache::<String, i32>::new(
            std::time::Duration::from_secs(1),
            Some(2)
        );

        // Test insertion and retrieval
        cache.insert("key1".to_string(), 42).await;
        cache.insert("key2".to_string(), 100).await;

        let value1 = cache.get(&"key1".to_string()).await;
        assert_eq!(value1, Some(42));

        let value2 = cache.get(&"key2".to_string()).await;
        assert_eq!(value2, Some(100));

        // Test size limit - start fresh to avoid unpredictable eviction
        cache.clear().await;
        cache.insert("test1".to_string(), 1).await;
        cache.insert("test2".to_string(), 2).await;
        assert_eq!(cache.size().await, 2);

        // Insert third item, should evict one
        cache.insert("test3".to_string(), 3).await;
        assert_eq!(cache.size().await, 2);

        // Verify test3 was inserted (it's the most recent)
        let value3 = cache.get(&"test3".to_string()).await;
        assert_eq!(value3, Some(3));

        // Test TTL expiration
        cache.clear().await; // Start fresh
        cache.insert_with_ttl("temp_key".to_string(), 999, std::time::Duration::from_millis(10)).await;
        tokio::time::sleep(std::time::Duration::from_millis(20)).await;
        let expired_value = cache.get(&"temp_key".to_string()).await;
        assert_eq!(expired_value, None);

        // Test removal
        cache.clear().await; // Start fresh
        cache.insert("remove_me".to_string(), 123).await;
        let removed = cache.remove(&"remove_me".to_string()).await;
        assert_eq!(removed, Some(123));
        let after_removal = cache.get(&"remove_me".to_string()).await;
        assert_eq!(after_removal, None);

        // Test cleanup
        cache.clear().await; // Start fresh
        cache.insert_with_ttl("expire1".to_string(), 1, std::time::Duration::from_millis(1)).await;
        cache.insert_with_ttl("expire2".to_string(), 2, std::time::Duration::from_millis(1)).await;
        tokio::time::sleep(std::time::Duration::from_millis(10)).await;
        let cleaned = cache.cleanup_expired().await;
        assert_eq!(cleaned, 2);

        // Test clear
        cache.clear().await;
        assert_eq!(cache.size().await, 0);
    }

    #[tokio::test]
    async fn test_generic_integration_client() {
        struct MockClient {
            value: i32,
        }

        let client = MockClient { value: 42 };
        let retry_policy = RetryPolicy::default();
        let integration_client = GenericIntegrationClient::new(client, retry_policy);

        // Test initial status
        assert_eq!(integration_client.connection_status().await, ConnectionStatus::NotConfigured);

        // Test status update
        integration_client.set_connection_status(ConnectionStatus::Connected).await;
        assert_eq!(integration_client.connection_status().await, ConnectionStatus::Connected);

        // Test client access
        assert_eq!(integration_client.client().value, 42);

        // Test health check without checker
        let health = integration_client.health_check().await.expect("Should return health");
        assert_eq!(health, HealthStatus::Unknown);
    }

    // =================================================================================================
    // Utility Function Tests
    // =================================================================================================

    #[test]
    fn test_utility_functions() {
        // Test ID generation
        let req_id = generate_request_id();
        assert!(req_id.starts_with("req_"));
        assert!(req_id.len() > 10);

        let sess_id = generate_session_id();
        assert!(sess_id.starts_with("sess_"));
        assert!(sess_id.len() > 10);

        // Test email validation
        assert!(validate_email("<EMAIL>"));
        assert!(validate_email("<EMAIL>"));
        assert!(!validate_email("invalid-email"));
        assert!(!validate_email("@domain.com"));
        assert!(!validate_email("user@"));

        // Test password hashing (placeholder implementation)
        let password = "test_password";
        let hash = hash_password(password);
        assert!(hash.starts_with("hashed_"));
        assert!(verify_password(password, &hash));
        assert!(!verify_password("wrong_password", &hash));

        // Test timestamp formatting
        let timestamp = DateTime::parse_from_rfc3339("2023-01-01T12:00:00Z").unwrap().with_timezone(&Utc);
        let formatted = format_timestamp(timestamp);
        assert!(formatted.contains("2023-01-01"));
        assert!(formatted.contains("12:00:00"));
        assert!(formatted.contains("UTC"));

        // Test input sanitization
        let dirty_input = "Hello\x00World\t\nTest";
        let clean = sanitize_input(dirty_input);
        assert_eq!(clean, "HelloWorld\t\nTest");

        // Test text truncation
        let long_text = "This is a very long text that should be truncated";
        let truncated = truncate_text(long_text, 20);
        assert_eq!(truncated, "This is a very lo...");
        assert_eq!(truncated.len(), 20);

        let short_text = "Short";
        let not_truncated = truncate_text(short_text, 20);
        assert_eq!(not_truncated, "Short");
    }

    // =================================================================================================
    // Helper Functions
    // =================================================================================================

    fn create_test_ui_config() -> UiConfig {
        UiConfig {
            server: ServerConfig {
                host: "localhost".to_string(),
                port: 8080,
                tls_enabled: false,
                cors_origins: vec!["http://localhost:3000".to_string()],
                max_connections: Some(100),
            },
            rabbitmq: RabbitMqConfig {
                host: "localhost".to_string(),
                port: 5672,
                username: "guest".to_string(),
                password: "guest".to_string(),
                virtual_host: None,
                exchange: "test_exchange".to_string(),
                queue_prefix: "test_".to_string(),
                websocket_enabled: true,
                websocket_port: Some(15674),
            },
            database: DatabaseConfig {
                host: "localhost".to_string(),
                port: 8000,
                database: "test_db".to_string(),
                username: Some("root".to_string()),
                password: Some("password".to_string()),
                connection_pool_size: Some(5),
            },
            auth: create_test_auth_config(),
            features: FeatureConfig {
                chat_enabled: true,
                agent_management_enabled: true,
                project_creation_enabled: true,
                mcp_integration_enabled: false,
                telemetry_enabled: true,
                debug_mode: true,
            },
            ui_settings: UiSettings {
                theme: "dark".to_string(),
                language: "en".to_string(),
                timezone: "UTC".to_string(),
                date_format: "YYYY-MM-DD".to_string(),
                max_chat_history: 50,
                auto_save_interval_seconds: 30,
                notification_settings: NotificationSettings {
                    desktop_notifications: true,
                    sound_enabled: false,
                    email_notifications: false,
                    severity_filter: vec![NotificationSeverity::Error],
                },
            },
        }
    }

    fn create_test_auth_config() -> AuthConfig {
        AuthConfig {
            jwt_secret: "test_secret".to_string(),
            jwt_expiry_hours: 1,
            refresh_token_expiry_days: 7,
            password_min_length: 6,
            require_email_verification: false,
            max_login_attempts: 3,
            lockout_duration_minutes: 5,
        }
    }

    fn create_test_user_session(session_id: &str, user_id: &str) -> UserSession {
        UserSession {
            session_id: session_id.to_string(),
            user_id: user_id.to_string(),
            state: SessionState::Active,
            created_at: Utc::now(),
            last_activity: Utc::now(),
            expires_at: Some(Utc::now() + chrono::Duration::hours(1)),
            device_info: DeviceInfo {
                device_id: "test_device".to_string(),
                device_type: DeviceType::Web,
                platform: "Test".to_string(),
                app_version: "1.0.0".to_string(),
                user_agent: None,
            },
            permissions: vec![Permission::ChatAccess],
        }
    }
}
