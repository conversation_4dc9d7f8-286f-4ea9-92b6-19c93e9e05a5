// =================================================================================================
// File: /prisma_ai/tests/integration/core_services_tests.rs
// =================================================================================================
// Purpose: Comprehensive integration tests for core UI services including authentication,
// session management, caching, and health monitoring with real database configurations.
// =================================================================================================

use std::sync::Arc;
use std::time::Duration;
use tokio::time::sleep;
use uuid::Uuid;

// Import test utilities
use crate::common::test_utils::{setup_test_database, cleanup_test_database, TestConfig};

// Import core services
use prisma_ai::prisma_ui::services::auth_service::{AuthService, LoginRequest, RegisterRequest};
use prisma_ai::prisma_ui::services::session_service::{SessionService, CreateSessionRequest};
use prisma_ai::prisma_ui::services::cache_service::{CacheService, CacheNamespace};
use prisma_ai::prisma_ui::services::health_service::UiHealthService;

// Import configuration and storage
use prisma_ai::storage::Database;
use prisma_ai::prisma::config::types::RedisConfig;
use prisma_ai::err::PrismaResult;

/// Test configuration for core services
struct CoreServicesTestConfig {
    database: Arc<Database>,
    redis_config: RedisConfig,
    jwt_secret: String,
    session_timeout_hours: i64,
}

impl CoreServicesTestConfig {
    async fn new() -> PrismaResult<Self> {
        let database = setup_test_database().await?;
        
        let redis_config = RedisConfig {
            host: "localhost".to_string(),
            port: 6379,
            password: "".to_string(),
            database: 1, // Use test database
            connection_timeout_ms: 5000,
            command_timeout_ms: 3000,
            max_connections: 5,
            version: "7.4.1".to_string(),
            cache: prisma_ai::prisma::config::types::RedisCacheConfig {
                default_ttl_seconds: 300, // 5 minutes for tests
                max_memory_policy: "allkeys-lru".to_string(),
                max_memory: "64mb".to_string(),
            },
            performance: prisma_ai::prisma::config::types::RedisPerformanceConfig {
                enable_pipelining: true,
                pipeline_batch_size: 50,
                enable_compression: false, // Disable for tests
                compression_threshold_bytes: 1024,
            },
        };

        Ok(Self {
            database,
            redis_config,
            jwt_secret: "test_jwt_secret_key_for_integration_tests".to_string(),
            session_timeout_hours: 1, // 1 hour for tests
        })
    }
}

#[tokio::test]
async fn test_auth_service_integration() -> PrismaResult<()> {
    let config = CoreServicesTestConfig::new().await?;

    // Clean up any existing user records to avoid enum deserialization issues
    let _ = config.database.client()
        .query("DELETE user")
        .await;

    // Initialize AuthService
    let auth_service = AuthService::new(
        config.database.clone(),
        "test_secret_key_for_jwt_testing_purposes_only".to_string(),
        24, // 24 hours token expiry
    ).await?;

    // Test user registration
    let register_request = RegisterRequest {
        email: "<EMAIL>".to_string(),
        password: "test_password_123".to_string(),
        device_id: Some("test_device_001".to_string()),
    };

    // First test direct serialization of User struct
    println!("Testing direct User struct serialization...");

    use chrono::Utc;
    use uuid::Uuid;
    use prisma_ai::prisma_ui::services::auth_service::User;

    let user_id = Uuid::new_v4().to_string();
    let now = Utc::now();

    let test_user = User {
        id: user_id.clone(),
        email: "<EMAIL>".to_string(),
        password_hash: "test_hash".to_string(),
        role: "user".to_string(),
        is_active: true,
        email_verified: false,
        created_at: now,
        updated_at: now,
        last_login: None,
    };

    // Try to serialize the user to JSON
    match serde_json::to_value(&test_user) {
        Ok(json_value) => {
            println!("✅ User serialization successful!");
            println!("JSON: {}", serde_json::to_string_pretty(&json_value).unwrap());
        }
        Err(e) => {
            println!("❌ User serialization failed: {}", e);
            return Err(prisma_ai::err::GenericError::from(prisma_ai::err::DomainError::SerializationError(
                format!("Failed to serialize user: {}", e)
            )));
        }
    }

    // Test user registration
    let register_request = RegisterRequest {
        email: "<EMAIL>".to_string(),
        password: "test_password_123".to_string(),
        device_id: Some("test_device_001".to_string()),
    };

    println!("Testing user registration...");
    let auth_response = auth_service.register(register_request).await?;
    assert!(!auth_response.token.is_empty());
    assert!(!auth_response.refresh_token.is_empty());
    assert_eq!(auth_response.user.email, "<EMAIL>");
    assert!(auth_response.user.is_active);

    // Test user login
    let login_request = LoginRequest {
        email: "<EMAIL>".to_string(),
        password: "test_password_123".to_string(),
        device_id: Some("test_device_001".to_string()),
    };

    let login_response = auth_service.login(login_request).await?;
    assert!(!login_response.token.is_empty());
    assert_eq!(login_response.user.email, "<EMAIL>");

    // Test token validation
    let claims = auth_service.validate_token(&login_response.token).await?;
    assert_eq!(claims.email, "<EMAIL>");

    // Test token refresh
    let refresh_response = auth_service.refresh_token(&login_response.refresh_token).await?;
    assert!(!refresh_response.token.is_empty());

    // Test password change
    auth_service.change_password(
        &auth_response.user.id,
        "test_password_123",
        "new_password_456"
    ).await?;

    // Test login with new password
    let new_login_request = LoginRequest {
        email: "<EMAIL>".to_string(),
        password: "new_password_456".to_string(),
        device_id: Some("test_device_001".to_string()),
    };

    let new_login_response = auth_service.login(new_login_request).await?;
    assert!(!new_login_response.token.is_empty());

    // Test user deactivation
    auth_service.deactivate_user(&auth_response.user.id).await?;

    // Test login with deactivated user should fail
    let deactivated_login_request = LoginRequest {
        email: "<EMAIL>".to_string(),
        password: "new_password_456".to_string(),
        device_id: Some("test_device_001".to_string()),
    };

    let deactivated_result = auth_service.login(deactivated_login_request).await;
    assert!(deactivated_result.is_err());

    println!("✅ AuthService integration tests passed");
    Ok(())
}

#[tokio::test]
async fn test_session_service_integration() -> PrismaResult<()> {
    let config = CoreServicesTestConfig::new().await?;
    
    // Initialize SessionService
    let session_service = SessionService::new(
        config.database.clone(),
        config.session_timeout_hours,
    ).await?;

    let user_id = Uuid::new_v4().to_string();
    let device_id = Uuid::new_v4().to_string();

    // Test session creation
    let create_request = CreateSessionRequest {
        user_id: user_id.clone(),
        device_id: device_id.clone(),
        preferences: None,
    };

    let session = session_service.create_session(create_request).await?;
    assert_eq!(session.user_id, user_id);
    assert_eq!(session.device_id, device_id);
    assert!(session.is_active);

    // Test session retrieval
    let retrieved_session = session_service.get_session(&session.id).await?;
    assert!(retrieved_session.is_some());
    let retrieved = retrieved_session.unwrap();
    assert_eq!(retrieved.id, session.id);
    assert_eq!(retrieved.user_id, user_id);

    // Test activity update
    session_service.update_activity(&session.id).await?;

    // Test session termination
    session_service.terminate_session(&session.id).await?;

    // Test session retrieval after termination
    let terminated_session = session_service.get_session(&session.id).await?;
    // Session might still exist but should be inactive
    if let Some(terminated) = terminated_session {
        assert!(!terminated.is_active);
    }

    // Test cleanup expired sessions
    let _cleaned_count = session_service.cleanup_expired_sessions().await?;

    println!("✅ SessionService integration tests passed");
    Ok(())
}

#[tokio::test]
async fn test_cache_service_integration() -> PrismaResult<()> {
    let config = CoreServicesTestConfig::new().await?;
    
    // Initialize CacheService
    let cache_service = CacheService::new(config.redis_config).await?;

    // Test health check
    let health = cache_service.health_check().await?;
    assert!(health);

    // Test basic set/get operations
    let test_data = "test_cache_value".to_string();
    cache_service.set(
        CacheNamespace::Custom("test".to_string()),
        "key1",
        &test_data,
        Some(Duration::from_secs(60))
    ).await?;

    let retrieved: Option<String> = cache_service.get(
        CacheNamespace::Custom("test".to_string()),
        "key1"
    ).await?;
    assert!(retrieved.is_some());
    assert_eq!(retrieved.unwrap(), test_data);

    // Test key existence
    let exists = cache_service.exists(
        CacheNamespace::Custom("test".to_string()),
        "key1"
    ).await?;
    assert!(exists);

    // Test TTL operations
    let ttl = cache_service.ttl(
        CacheNamespace::Custom("test".to_string()),
        "key1"
    ).await?;
    assert!(ttl.is_some());

    // Test key deletion
    let deleted = cache_service.delete(
        CacheNamespace::Custom("test".to_string()),
        "key1"
    ).await?;
    assert!(deleted);

    // Test key doesn't exist after deletion
    let exists_after_delete = cache_service.exists(
        CacheNamespace::Custom("test".to_string()),
        "key1"
    ).await?;
    assert!(!exists_after_delete);

    // Test pattern invalidation
    let value1 = "value1".to_string();
    let value2 = "value2".to_string();

    cache_service.set(
        CacheNamespace::Custom("test".to_string()),
        "pattern_key1",
        &value1,
        None
    ).await?;

    cache_service.set(
        CacheNamespace::Custom("test".to_string()),
        "pattern_key2",
        &value2,
        None
    ).await?;

    let invalidation_event = cache_service.invalidate_pattern("test:pattern_*").await?;
    assert_eq!(invalidation_event.affected_keys.len(), 2);

    // Test cache statistics
    let stats = cache_service.get_stats().await?;
    assert!(stats.total_keys >= 0);
    assert!(stats.hit_rate >= 0.0);

    println!("✅ CacheService integration tests passed");
    Ok(())
}

#[tokio::test]
async fn test_health_service_integration() -> PrismaResult<()> {
    let config = CoreServicesTestConfig::new().await?;
    
    // Initialize HealthService
    let health_config = prisma_ai::prisma_ui::services::health_service::UiHealthServiceConfig::default();
    let health_service = prisma_ai::prisma_ui::services::health_service::UiHealthService::new(health_config);

    // Test health summary
    let health_summary = health_service.get_health_summary().await?;
    // The summary should be available even if no services are registered yet

    // Test service health reporting
    let test_report = prisma_ai::prisma_ui::services::health_service::create_ui_health_report(
        "test_service",
        prisma_ai::prisma_ui::services::health_service::UiHealthStatus::Healthy,
        Some("Test service is running".to_string()),
        None,
    );

    health_service.report_service_health(test_report).await?;

    // Get updated health summary
    let updated_summary = health_service.get_health_summary().await?;
    // Should now include our test service report

    println!("✅ HealthService integration tests passed");
    Ok(())
}

#[tokio::test]
async fn test_cross_service_integration() -> PrismaResult<()> {
    let config = CoreServicesTestConfig::new().await?;
    
    // Initialize all services
    let auth_service = AuthService::new(
        config.database.clone(),
        config.jwt_secret.clone(),
        config.session_timeout_hours,
    ).await?;

    let session_service = SessionService::new(
        config.database.clone(),
        config.session_timeout_hours,
    ).await?;

    let cache_service = CacheService::new(config.redis_config).await?;

    // Test integrated workflow: Register -> Login -> Create Session -> Cache User Data
    
    // 1. Register user
    let register_request = RegisterRequest {
        email: "<EMAIL>".to_string(),
        password: "integration_password_123".to_string(),
        device_id: Some("integration_device_001".to_string()),
    };

    let auth_response = auth_service.register(register_request).await?;
    
    // 2. Create session for the user
    let create_session_request = CreateSessionRequest {
        user_id: auth_response.user.id.clone(),
        device_id: "integration_device_001".to_string(),
        preferences: None,
    };

    let session = session_service.create_session(create_session_request).await?;
    
    // 3. Cache user data
    cache_service.set(
        CacheNamespace::User,
        &auth_response.user.id,
        &auth_response.user,
        Some(Duration::from_secs(300))
    ).await?;

    // 4. Retrieve cached user data
    let cached_user: Option<prisma_ai::prisma_ui::services::auth_service::UserInfo> = 
        cache_service.get(CacheNamespace::User, &auth_response.user.id).await?;
    
    assert!(cached_user.is_some());
    let cached = cached_user.unwrap();
    assert_eq!(cached.email, "<EMAIL>");

    // 5. Update session activity
    session_service.update_activity(&session.id).await?;

    // 6. Validate token
    let claims = auth_service.validate_token(&auth_response.token).await?;
    assert_eq!(claims.sub, auth_response.user.id);

    // 7. Clean up
    session_service.terminate_session(&session.id).await?;
    cache_service.delete(CacheNamespace::User, &auth_response.user.id).await?;

    println!("✅ Cross-service integration tests passed");
    Ok(())
}

// Cleanup function to run after tests
async fn cleanup_core_services_tests() -> PrismaResult<()> {
    cleanup_test_database().await?;
    Ok(())
}
