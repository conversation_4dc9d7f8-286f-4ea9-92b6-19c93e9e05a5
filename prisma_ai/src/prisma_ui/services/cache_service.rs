// =================================================================================================
// File: /prisma_ai/src/prisma_ui/services/cache_service.rs
// =================================================================================================
// Purpose: Caching service for UI performance optimization using SurrealDB and in-memory storage.
// This service provides multi-layer caching functionality to optimize UI performance, reduce
// database load, and improve response times for frequently accessed data. It uses SurrealDB
// for persistent caching and in-memory storage for high-speed access patterns.
// =================================================================================================
// Internal Dependencies:
// - auth_service.rs: Authentication data caching and user credential optimization
// - session_service.rs: Session data caching and multi-device state optimization
// - chat_service.rs: Chat message caching and conversation history optimization
// - types.rs: Cache data types and storage structures
// - traits.rs: Cache service trait definitions and abstractions
// =================================================================================================
// External Dependencies:
// - SurrealDB: Persistent cache storage, data indexing, and cross-device cache sharing
// - tokio: Async cache operations and concurrent access management
// - serde: Cache data serialization and deserialization for storage operations
// - chrono: Cache expiration tracking and time-based invalidation management
// - std::collections::HashMap: In-memory cache storage for high-speed data access
// =================================================================================================
// Module Interactions:
// - Provides caching services to all UI service components for performance optimization
// - Integrates with SurrealDB for persistent cache storage and cross-device sharing
// - Coordinates with session service for user-specific cache management
// - Handles cache invalidation events via RabbitMQ for real-time cache consistency
// - Optimizes data access patterns for chat, agent, project, and participant services
// =================================================================================================
// Cache Service Features:
// - Multi-layer Caching: In-memory + SurrealDB persistent cache for optimal performance
// - Data Optimization: Frequently accessed UI data, user preferences, and system metadata
// - Cache Invalidation: Time-based expiration and event-driven cache invalidation
// - Cross-device Sharing: Shared cache data across multiple user devices via SurrealDB
// - Performance Monitoring: Cache hit/miss tracking and performance metrics collection
// =================================================================================================
// Cache Architecture:
// ┌─────────────────┐    Cache Requests┌──────────────────────┐    Storage Layers ┌─────────────────┐
// │ UI Services     │ ──────────→      │ Cache Service        │ ──────────→       │ Cache Storage   │
// │ - Chat Service  │                  │ ├─Memory Cache       │                   │ - In-Memory     │
// │ - Auth Service  │                  │ │ (Fast Access)      │                   │   HashMap       │
// │ - Session Svc   │                  │ ├─Persistent Cache   │                   │ - SurrealDB     │
// │ - Agent Service │                  │ │ (Cross-device)     │                   │   Persistent    │
// └─────────────────┘                  │ ├─Cache Invalidation │                   │ - Expiration    │
//                                      │ └─Performance Monitor│                   │   Management    │
//                                      └──────────────────────┘                   └─────────────────┘
//                                               ↓                                           ↓
//                                      ┌──────────────────────┐                   ┌─────────────────┐
//                                      │ Cache Coordination   │                   │ Data Sources    │
//                                      │ - Hit/Miss Tracking  │ ←─────────────────│ - SurrealDB     │
//                                      │ - Invalidation Events│                   │ - Service APIs  │
//                                      │ - Performance Metrics│                   │ - External Data │
//                                      └──────────────────────┘                   └─────────────────┘
// =================================================================================================

use std::sync::Arc;
use std::time::Duration;
use tokio::sync::RwLock;
use serde::{Serialize, Deserialize, de::DeserializeOwned};
use chrono::{DateTime, Utc};
use redis::{Client, Connection, Commands, AsyncCommands, RedisResult};
use redis::aio::ConnectionManager;
use tracing::{info, error, debug, warn};

// Import error handling
use crate::err::{PrismaResult, GenericError as PrismaError, DomainError};

// Import configuration
use crate::prisma::config::{RedisConfig, SettingsManager};

/// Cache entry metadata
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CacheEntry<T> {
    pub data: T,
    pub created_at: DateTime<Utc>,
    pub expires_at: Option<DateTime<Utc>>,
    pub access_count: u64,
    pub last_accessed: DateTime<Utc>,
}

/// Cache statistics for monitoring
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CacheStats {
    pub total_keys: u64,
    pub hits: u64,
    pub misses: u64,
    pub hit_rate: f64,
    pub memory_usage: u64,
    pub expired_keys: u64,
}

/// Cache invalidation event
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CacheInvalidationEvent {
    pub pattern: String,
    pub reason: InvalidationReason,
    pub timestamp: DateTime<Utc>,
    pub affected_keys: Vec<String>,
}

/// Reasons for cache invalidation
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum InvalidationReason {
    Expired,
    Manual,
    DataUpdated,
    MemoryPressure,
    PatternMatch,
}

/// Cache key namespace for different data types
#[derive(Debug, Clone)]
pub enum CacheNamespace {
    User,
    Session,
    Chat,
    Agent,
    Project,
    Preferences,
    Authentication,
    Custom(String),
}

impl CacheNamespace {
    fn prefix(&self) -> &str {
        match self {
            CacheNamespace::User => "user:",
            CacheNamespace::Session => "session:",
            CacheNamespace::Chat => "chat:",
            CacheNamespace::Agent => "agent:",
            CacheNamespace::Project => "project:",
            CacheNamespace::Preferences => "prefs:",
            CacheNamespace::Authentication => "auth:",
            CacheNamespace::Custom(prefix) => prefix,
        }
    }

    fn key(&self, id: &str) -> String {
        format!("{}{}", self.prefix(), id)
    }
}

/// Cache service for UI performance optimization
pub struct CacheService {
    connection_manager: Arc<ConnectionManager>,
    config: RedisConfig,
    stats: Arc<RwLock<CacheStats>>,
}

impl CacheService {
    /// Create a new CacheService instance
    pub async fn new(config: RedisConfig) -> PrismaResult<Self> {
        info!("Initializing Redis cache service at {}:{}", config.host, config.port);

        // Create Redis client
        let client = Client::open(config.connection_url())
            .map_err(|e| PrismaError::from(DomainError::ConnectionError(
                format!("Failed to create Redis client: {}", e)
            )))?;

        // Create connection manager for async operations
        let connection_manager = ConnectionManager::new(client).await
            .map_err(|e| PrismaError::from(DomainError::ConnectionError(
                format!("Failed to create Redis connection manager: {}", e)
            )))?;

        let stats = Arc::new(RwLock::new(CacheStats {
            total_keys: 0,
            hits: 0,
            misses: 0,
            hit_rate: 0.0,
            memory_usage: 0,
            expired_keys: 0,
        }));

        info!("Redis cache service initialized successfully");

        Ok(Self {
            connection_manager: Arc::new(connection_manager),
            config,
            stats,
        })
    }

    /// Create CacheService from settings configuration
    pub async fn from_settings(settings_manager: &SettingsManager) -> PrismaResult<Self> {
        let redis_config = settings_manager.get_settings().redis.clone();
        Self::new(redis_config).await
    }

    /// Set a value in the cache with optional TTL
    pub async fn set<T>(&self, namespace: CacheNamespace, key: &str, value: &T, ttl: Option<Duration>) -> PrismaResult<()>
    where
        T: Serialize + for<'de> serde::Deserialize<'de> + Send + Sync,
    {
        let cache_key = namespace.key(key);
        debug!("Setting cache key: {}", cache_key);

        let entry = CacheEntry {
            data: value,
            created_at: Utc::now(),
            expires_at: ttl.map(|d| Utc::now() + chrono::Duration::from_std(d).unwrap()),
            access_count: 0,
            last_accessed: Utc::now(),
        };

        let serialized = serde_json::to_string(&entry)
            .map_err(|e| PrismaError::from(DomainError::SerializationError(
                format!("Failed to serialize cache entry: {}", e)
            )))?;

        let mut conn = (*self.connection_manager).clone();

        if let Some(ttl_duration) = ttl {
            let ttl_seconds = ttl_duration.as_secs();
            conn.set_ex(&cache_key, serialized, ttl_seconds).await
                .map_err(|e| PrismaError::from(DomainError::CacheError(
                    format!("Failed to set cache key with TTL: {}", e)
                )))?;
        } else {
            conn.set(&cache_key, serialized).await
                .map_err(|e| PrismaError::from(DomainError::CacheError(
                    format!("Failed to set cache key: {}", e)
                )))?;
        }

        // Update stats
        {
            let mut stats = self.stats.write().await;
            stats.total_keys += 1;
        }

        Ok(())
    }

    /// Get a value from the cache
    pub async fn get<T>(&self, namespace: CacheNamespace, key: &str) -> PrismaResult<Option<T>>
    where
        T: Serialize + for<'de> serde::Deserialize<'de> + Send + Sync,
    {
        let cache_key = namespace.key(key);
        debug!("Getting cache key: {}", cache_key);

        let mut conn = (*self.connection_manager).clone();

        let result: RedisResult<String> = conn.get(&cache_key).await;

        match result {
            Ok(serialized) => {
                let mut entry: CacheEntry<T> = serde_json::from_str(&serialized)
                    .map_err(|e| PrismaError::from(DomainError::SerializationError(
                        format!("Failed to deserialize cache entry: {}", e)
                    )))?;

                // Check if entry has expired
                if let Some(expires_at) = entry.expires_at {
                    if Utc::now() > expires_at {
                        // Entry has expired, remove it
                        self.delete(namespace, key).await?;

                        // Update stats
                        {
                            let mut stats = self.stats.write().await;
                            stats.misses += 1;
                            stats.expired_keys += 1;
                            stats.hit_rate = stats.hits as f64 / (stats.hits + stats.misses) as f64;
                        }

                        return Ok(None);
                    }
                }

                // Update access metadata
                entry.access_count += 1;
                entry.last_accessed = Utc::now();

                // Update the entry in cache with new metadata
                let updated_serialized = serde_json::to_string(&entry)
                    .map_err(|e| PrismaError::from(DomainError::SerializationError(
                        format!("Failed to serialize updated cache entry: {}", e)
                    )))?;

                let _: RedisResult<()> = conn.set(&cache_key, updated_serialized).await;

                // Update stats
                {
                    let mut stats = self.stats.write().await;
                    stats.hits += 1;
                    stats.hit_rate = stats.hits as f64 / (stats.hits + stats.misses) as f64;
                }

                Ok(Some(entry.data))
            }
            Err(_) => {
                // Key not found
                {
                    let mut stats = self.stats.write().await;
                    stats.misses += 1;
                    stats.hit_rate = stats.hits as f64 / (stats.hits + stats.misses) as f64;
                }

                Ok(None)
            }
        }
    }

    /// Delete a key from the cache
    pub async fn delete(&self, namespace: CacheNamespace, key: &str) -> PrismaResult<bool> {
        let cache_key = namespace.key(key);
        debug!("Deleting cache key: {}", cache_key);

        let mut conn = (*self.connection_manager).clone();

        let deleted: u32 = conn.del(&cache_key).await
            .map_err(|e| PrismaError::from(DomainError::CacheError(
                format!("Failed to delete cache key: {}", e)
            )))?;

        if deleted > 0 {
            // Update stats
            {
                let mut stats = self.stats.write().await;
                stats.total_keys = stats.total_keys.saturating_sub(1);
            }
        }

        Ok(deleted > 0)
    }

    /// Check if a key exists in the cache
    pub async fn exists(&self, namespace: CacheNamespace, key: &str) -> PrismaResult<bool> {
        let cache_key = namespace.key(key);

        let mut conn = (*self.connection_manager).clone();

        let exists: bool = conn.exists(&cache_key).await
            .map_err(|e| PrismaError::from(DomainError::CacheError(
                format!("Failed to check cache key existence: {}", e)
            )))?;

        Ok(exists)
    }

    /// Set TTL for an existing key
    pub async fn expire(&self, namespace: CacheNamespace, key: &str, ttl: Duration) -> PrismaResult<bool> {
        let cache_key = namespace.key(key);

        let mut conn = (*self.connection_manager).clone();

        let result: bool = conn.expire(&cache_key, ttl.as_secs() as i64).await
            .map_err(|e| PrismaError::from(DomainError::CacheError(
                format!("Failed to set TTL for cache key: {}", e)
            )))?;

        Ok(result)
    }

    /// Get TTL for a key
    pub async fn ttl(&self, namespace: CacheNamespace, key: &str) -> PrismaResult<Option<Duration>> {
        let cache_key = namespace.key(key);

        let mut conn = (*self.connection_manager).clone();

        let ttl_seconds: i64 = conn.ttl(&cache_key).await
            .map_err(|e| PrismaError::from(DomainError::CacheError(
                format!("Failed to get TTL for cache key: {}", e)
            )))?;

        match ttl_seconds {
            -1 => Ok(None), // Key exists but has no TTL
            -2 => Ok(None), // Key does not exist
            seconds if seconds > 0 => Ok(Some(Duration::from_secs(seconds as u64))),
            _ => Ok(None),
        }
    }

    /// Invalidate cache keys by pattern
    pub async fn invalidate_pattern(&self, pattern: &str) -> PrismaResult<CacheInvalidationEvent> {
        info!("Invalidating cache keys matching pattern: {}", pattern);

        let mut conn = (*self.connection_manager).clone();

        // Get all keys matching the pattern
        let keys: Vec<String> = conn.keys(pattern).await
            .map_err(|e| PrismaError::from(DomainError::CacheError(
                format!("Failed to get keys matching pattern: {}", e)
            )))?;

        let affected_count = keys.len();

        if !keys.is_empty() {
            // Delete all matching keys
            let deleted: u32 = conn.del(&keys).await
                .map_err(|e| PrismaError::from(DomainError::CacheError(
                    format!("Failed to delete keys matching pattern: {}", e)
                )))?;

            // Update stats
            {
                let mut stats = self.stats.write().await;
                stats.total_keys = stats.total_keys.saturating_sub(deleted as u64);
            }
        }

        let event = CacheInvalidationEvent {
            pattern: pattern.to_string(),
            reason: InvalidationReason::PatternMatch,
            timestamp: Utc::now(),
            affected_keys: keys,
        };

        info!("Invalidated {} cache keys matching pattern: {}", affected_count, pattern);
        Ok(event)
    }

    /// Clear all cache entries
    pub async fn clear_all(&self) -> PrismaResult<()> {
        warn!("Clearing all cache entries");

        let mut conn = (*self.connection_manager).clone();

        redis::cmd("FLUSHDB").query_async(&mut conn).await
            .map_err(|e| PrismaError::from(DomainError::CacheError(
                format!("Failed to clear all cache entries: {}", e)
            )))?;

        // Reset stats
        {
            let mut stats = self.stats.write().await;
            *stats = CacheStats {
                total_keys: 0,
                hits: 0,
                misses: 0,
                hit_rate: 0.0,
                memory_usage: 0,
                expired_keys: 0,
            };
        }

        info!("All cache entries cleared");
        Ok(())
    }

    /// Get cache statistics
    pub async fn get_stats(&self) -> PrismaResult<CacheStats> {
        let mut conn = (*self.connection_manager).clone();

        // Get Redis info for memory usage
        let info: String = redis::cmd("INFO").arg("memory").query_async(&mut conn).await
            .map_err(|e| PrismaError::from(DomainError::CacheError(
                format!("Failed to get Redis memory info: {}", e)
            )))?;

        // Parse memory usage from info string
        let memory_usage = self.parse_memory_usage(&info);

        // Get current key count
        let key_count: u64 = redis::cmd("DBSIZE").query_async(&mut conn).await
            .map_err(|e| PrismaError::from(DomainError::CacheError(
                format!("Failed to get database size: {}", e)
            )))?;

        let mut stats = self.stats.write().await;
        stats.total_keys = key_count;
        stats.memory_usage = memory_usage;

        Ok(stats.clone())
    }

    /// Parse memory usage from Redis INFO output
    fn parse_memory_usage(&self, info: &str) -> u64 {
        for line in info.lines() {
            if line.starts_with("used_memory:") {
                if let Some(value) = line.split(':').nth(1) {
                    return value.parse().unwrap_or(0);
                }
            }
        }
        0
    }

    /// Cleanup expired keys
    pub async fn cleanup_expired(&self) -> PrismaResult<u64> {
        debug!("Cleaning up expired cache keys");

        // Redis automatically handles expired key cleanup, but we can trigger it manually
        let mut conn = (*self.connection_manager).clone();

        // Get all keys and check their TTL
        let all_keys: Vec<String> = conn.keys("*").await
            .map_err(|e| PrismaError::from(DomainError::CacheError(
                format!("Failed to get all keys for cleanup: {}", e)
            )))?;

        let mut expired_count = 0;

        for key in all_keys {
            let ttl: i64 = conn.ttl(&key).await.unwrap_or(-1);
            if ttl == -2 {
                // Key has expired and been removed by Redis
                expired_count += 1;
            }
        }

        // Update stats
        {
            let mut stats = self.stats.write().await;
            stats.expired_keys += expired_count;
        }

        debug!("Cleaned up {} expired cache keys", expired_count);
        Ok(expired_count)
    }

    /// Set multiple values at once (pipeline operation)
    pub async fn set_multiple<T>(&self, namespace: CacheNamespace, entries: Vec<(String, T)>, ttl: Option<Duration>) -> PrismaResult<()>
    where
        T: Serialize + for<'de> serde::Deserialize<'de> + Send + Sync,
    {
        if entries.is_empty() {
            return Ok(());
        }

        debug!("Setting {} cache entries in pipeline", entries.len());

        let mut conn = (*self.connection_manager).clone();
        let mut pipe = redis::pipe();

        for (key, value) in entries {
            let cache_key = namespace.key(&key);

            let entry = CacheEntry {
                data: value,
                created_at: Utc::now(),
                expires_at: ttl.map(|d| Utc::now() + chrono::Duration::from_std(d).unwrap()),
                access_count: 0,
                last_accessed: Utc::now(),
            };

            let serialized = serde_json::to_string(&entry)
                .map_err(|e| PrismaError::from(DomainError::SerializationError(
                    format!("Failed to serialize cache entry: {}", e)
                )))?;

            if let Some(ttl_duration) = ttl {
                pipe.set_ex(&cache_key, serialized, ttl_duration.as_secs());
            } else {
                pipe.set(&cache_key, serialized);
            }
        }

        pipe.query_async(&mut conn).await
            .map_err(|e| PrismaError::from(DomainError::CacheError(
                format!("Failed to execute pipeline: {}", e)
            )))?;

        Ok(())
    }

    /// Get connection health status
    pub async fn health_check(&self) -> PrismaResult<bool> {
        let mut conn = (*self.connection_manager).clone();

        let pong: String = redis::cmd("PING").query_async(&mut conn).await
            .map_err(|e| PrismaError::from(DomainError::CacheError(
                format!("Redis health check failed: {}", e)
            )))?;

        Ok(pong == "PONG")
    }
}
