// =================================================================================================
// File: /prisma_ai/src/prisma_ui/services/auth_service.rs
// =================================================================================================
// Purpose: Authentication service for user management and JWT-based security across multiple devices.
// This service handles user registration, login, logout, JWT token generation/validation, and
// role-based permissions for the PRISMA UI system. It supports multi-device authentication
// with session coordination and secure credential management.
// =================================================================================================
// Internal Dependencies:
// - session_service.rs: Session management integration for authenticated users
// - cache_service.rs: Authentication token caching and user data optimization
// - types.rs: Authentication data types and user credential structures
// - traits.rs: Authentication service trait definitions and abstractions
// - ../middleware/auth_middleware.rs: JWT validation middleware integration
// =================================================================================================
// External Dependencies:
// - SurrealDB: User credential storage, role management, and authentication history
// - JWT (jsonwebtoken): Token generation, validation, and expiration management
// - bcrypt: Password hashing and verification for secure credential storage
// - RabbitMQ: Authentication event publishing and multi-device coordination
// - uuid: Unique identifier generation for users, sessions, and authentication tokens
// =================================================================================================
// Module Interactions:
// - Integrates with session service for authenticated user session management
// - Coordinates with middleware layer for JWT validation and authentication checks
// - Manages user credential storage and retrieval via SurrealDB integration
// - Publishes authentication events via RabbitMQ for real-time UI updates
// - Provides authentication context to all UI services requiring user verification
// =================================================================================================
// Authentication Features:
// - User Management: Registration, login, logout, and profile management operations
// - JWT Security: Token generation, validation, refresh, and expiration handling
// - Multi-device Support: Device registration, authentication, and session coordination
// - Role-based Access: User roles, permissions, and access control management
// - Security: Password hashing, token encryption, and secure credential handling
// =================================================================================================
// Authentication Architecture:
// ┌─────────────────┐    Auth Requests ┌──────────────────────┐    Validation    ┌─────────────────┐
// │ Flutter UI      │ ──────────→      │ Authentication       │ ──────────→      │ Security        │
// │ (Multi-device)  │                  │ Service              │                  │ - JWT Tokens    │
// │ - Login Forms   │                  │ ├─User Management    │                  │ - Password Hash │
// │ - Registration  │                  │ ├─JWT Generation     │                  │ - Role Checking │
// │ - Device Auth   │                  │ ├─Multi-device Sync  │                  │ - Session Valid │
// └─────────────────┘                  │ └─Security Validation│                  └─────────────────┘
//                                      └──────────────────────┘                           ↓
//                                               ↓                                  ┌─────────────────┐
//                                      ┌──────────────────────┐                   │ Storage         │
//                                      │ Session & Cache      │ ←─────────────────│ - SurrealDB     │
//                                      │ Integration          │                   │ - User Data     │
//                                      └──────────────────────┘                   │ - Auth History  │
//                                                                                 └─────────────────┘
// =================================================================================================

use std::sync::Arc;
use tokio::sync::RwLock;
use serde::{Serialize, Deserialize};
use chrono::{DateTime, Utc, Duration};
use uuid::Uuid;
use jsonwebtoken::{encode, decode, Header, Algorithm, Validation, EncodingKey, DecodingKey};
use bcrypt::{hash, verify, DEFAULT_COST};
use tracing::{info, error, debug};

// Import error handling
use crate::err::{PrismaResult, GenericError as PrismaError, DomainError};

// Import storage
use crate::storage::{Database, DataStore};

// Custom serialization for datetime to ensure compatibility with SurrealDB
mod datetime_format {
    use chrono::{DateTime, Utc};
    use serde::{self, Deserialize, Deserializer, Serializer};

    pub fn serialize<S>(dt: &DateTime<Utc>, serializer: S) -> Result<S::Ok, S::Error>
    where
        S: Serializer,
    {
        // Format as RFC3339 for compatibility with SurrealDB
        serializer.serialize_str(&dt.to_rfc3339())
    }

    pub fn deserialize<'de, D>(deserializer: D) -> Result<DateTime<Utc>, D::Error>
    where
        D: Deserializer<'de>,
    {
        let s = String::deserialize(deserializer)?;
        DateTime::parse_from_rfc3339(&s)
            .map(|dt| dt.with_timezone(&Utc))
            .map_err(serde::de::Error::custom)
    }
}

// Separate module for Optional datetime fields
mod datetime_option_format {
    use chrono::{DateTime, Utc};
    use serde::{self, Deserialize, Deserializer, Serializer};

    pub fn serialize<S>(dt: &Option<DateTime<Utc>>, serializer: S) -> Result<S::Ok, S::Error>
    where
        S: Serializer,
    {
        match dt {
            Some(dt) => serializer.serialize_str(&dt.to_rfc3339()),
            None => serializer.serialize_none(),
        }
    }

    pub fn deserialize<'de, D>(deserializer: D) -> Result<Option<DateTime<Utc>>, D::Error>
    where
        D: Deserializer<'de>,
    {
        let s = Option::<String>::deserialize(deserializer)?;
        match s {
            Some(s) => {
                DateTime::parse_from_rfc3339(&s)
                    .map(|dt| Some(dt.with_timezone(&Utc)))
                    .map_err(serde::de::Error::custom)
            }
            None => Ok(None),
        }
    }
}

/// User data structure for database storage
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct User {
    pub id: String,
    pub email: String,
    pub password_hash: String,
    pub role: String, // Store as string for SurrealDB compatibility
    pub is_active: bool,
    pub email_verified: bool,
    #[serde(with = "datetime_format")]
    pub created_at: DateTime<Utc>,
    #[serde(with = "datetime_format")]
    pub updated_at: DateTime<Utc>,
    #[serde(with = "datetime_option_format")]
    pub last_login: Option<DateTime<Utc>>,
}

/// User roles for access control
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
#[serde(rename_all = "lowercase")]
pub enum UserRole {
    Admin,
    User,
    Guest,
}

/// JWT claims structure
#[derive(Debug, Serialize, Deserialize)]
pub struct Claims {
    pub sub: String,      // Subject (user ID)
    pub email: String,    // User email
    pub role: UserRole,   // User role
    pub exp: i64,         // Expiration time
    pub iat: i64,         // Issued at
    pub device_id: Option<String>, // Device identifier for multi-device support
}

/// Authentication request for login
#[derive(Debug, Deserialize)]
pub struct LoginRequest {
    pub email: String,
    pub password: String,
    pub device_id: Option<String>,
}

/// User registration request
#[derive(Debug, Deserialize)]
pub struct RegisterRequest {
    pub email: String,
    pub password: String,
    pub device_id: Option<String>,
}

/// Authentication response with JWT token
#[derive(Debug, Serialize)]
pub struct AuthResponse {
    pub token: String,
    pub refresh_token: String,
    pub user: UserInfo,
    #[serde(with = "datetime_format")]
    pub expires_at: DateTime<Utc>,
}

/// User information for responses (without sensitive data)
#[derive(Debug, Serialize, Deserialize)]
pub struct UserInfo {
    pub id: String,
    pub email: String,
    pub role: String, // Store as string for SurrealDB compatibility
    pub is_active: bool,
    pub email_verified: bool,
    #[serde(with = "datetime_format")]
    pub created_at: DateTime<Utc>,
    #[serde(with = "datetime_option_format")]
    pub last_login: Option<DateTime<Utc>>,
}

/// Device information for multi-device support
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Device {
    pub id: String,
    pub user_id: String,
    pub device_name: String,
    pub device_type: String,
    #[serde(with = "datetime_format")]
    pub last_active: DateTime<Utc>,
    pub is_active: bool,
    #[serde(with = "datetime_format")]
    pub created_at: DateTime<Utc>,
}

/// Authentication service for user management and JWT security
pub struct AuthService {
    database: Arc<Database>,
    jwt_secret: String,
    token_expiry_hours: i64,
}

impl AuthService {
    /// Create a new AuthService instance
    pub async fn new(database: Arc<Database>, jwt_secret: String, token_expiry_hours: i64) -> PrismaResult<Self> {
        Ok(Self {
            database,
            jwt_secret,
            token_expiry_hours,
        })
    }

    /// Register a new user with email and password
    pub async fn register(&self, request: RegisterRequest) -> PrismaResult<AuthResponse> {
        info!("Attempting to register user with email: {}", request.email);

        // Check if user already exists
        if self.get_user_by_email(&request.email).await?.is_some() {
            return Err(PrismaError::from(DomainError::ValidationError(
                "User with this email already exists".to_string()
            )));
        }

        // Validate email format
        if !self.is_valid_email(&request.email) {
            return Err(PrismaError::from(DomainError::ValidationError(
                "Invalid email format".to_string()
            )));
        }

        // Validate password strength
        if !self.is_valid_password(&request.password) {
            return Err(PrismaError::from(DomainError::ValidationError(
                "Password must be at least 8 characters long".to_string()
            )));
        }

        // Hash the password
        let password_hash = hash(&request.password, DEFAULT_COST)
            .map_err(|e| PrismaError::from(DomainError::AuthError(
                format!("Failed to hash password: {}", e)
            )))?;

        // Create new user
        let user_id = Uuid::new_v4().to_string();
        let now = Utc::now();

        let user = User {
            id: user_id.clone(),
            email: request.email.clone(),
            password_hash,
            role: "user".to_string(), // Use string representation
            is_active: true,
            email_verified: false, // TODO: Implement email verification
            created_at: now,
            updated_at: now,
            last_login: None,
        };

        // Store user in database
        self.create_user(&user).await?;

        // Register device if provided
        if let Some(device_id) = &request.device_id {
            self.register_device(&user_id, device_id, "Unknown", "Unknown").await?;
        }

        // Generate JWT token
        let auth_response = self.generate_auth_response(&user, request.device_id).await?;

        info!("Successfully registered user: {}", request.email);
        Ok(auth_response)
    }

    /// Authenticate user with email and password
    pub async fn login(&self, request: LoginRequest) -> PrismaResult<AuthResponse> {
        info!("Attempting to login user with email: {}", request.email);

        // Get user by email
        let user = self.get_user_by_email(&request.email).await?
            .ok_or_else(|| PrismaError::from(DomainError::AuthError(
                "Invalid email or password".to_string()
            )))?;

        // Check if user is active
        if !user.is_active {
            return Err(PrismaError::from(DomainError::AuthError(
                "User account is deactivated".to_string()
            )));
        }

        // Verify password
        let password_valid = verify(&request.password, &user.password_hash)
            .map_err(|e| PrismaError::from(DomainError::AuthError(
                format!("Password verification failed: {}", e)
            )))?;

        if !password_valid {
            return Err(PrismaError::from(DomainError::AuthError(
                "Invalid email or password".to_string()
            )));
        }

        // Update last login time
        let mut updated_user = user.clone();
        updated_user.last_login = Some(Utc::now());
        updated_user.updated_at = Utc::now();
        self.update_user(&updated_user).await?;

        // Update device activity if provided
        if let Some(device_id) = &request.device_id {
            self.update_device_activity(&user.id, device_id).await?;
        }

        // Generate JWT token
        let auth_response = self.generate_auth_response(&updated_user, request.device_id).await?;

        info!("Successfully logged in user: {}", request.email);
        Ok(auth_response)
    }

    /// Validate JWT token and return user claims
    pub async fn validate_token(&self, token: &str) -> PrismaResult<Claims> {
        let decoding_key = DecodingKey::from_secret(self.jwt_secret.as_ref());
        let validation = Validation::new(Algorithm::HS256);

        let token_data = decode::<Claims>(token, &decoding_key, &validation)
            .map_err(|e| PrismaError::from(DomainError::AuthError(
                format!("Invalid token: {}", e)
            )))?;

        // Check if user still exists and is active
        let user = self.get_user_by_id(&token_data.claims.sub).await?
            .ok_or_else(|| PrismaError::from(DomainError::AuthError(
                "User not found".to_string()
            )))?;

        if !user.is_active {
            return Err(PrismaError::from(DomainError::AuthError(
                "User account is deactivated".to_string()
            )));
        }

        Ok(token_data.claims)
    }

    /// Refresh JWT token
    pub async fn refresh_token(&self, refresh_token: &str) -> PrismaResult<AuthResponse> {
        // For simplicity, we'll validate the refresh token the same way as access token
        // In production, you might want separate refresh token handling
        let claims = self.validate_token(refresh_token).await?;

        let user = self.get_user_by_id(&claims.sub).await?
            .ok_or_else(|| PrismaError::from(DomainError::AuthError(
                "User not found".to_string()
            )))?;

        self.generate_auth_response(&user, claims.device_id).await
    }

    /// Get user by ID
    pub async fn get_user_by_id(&self, user_id: &str) -> PrismaResult<Option<User>> {
        let users: Vec<User> = self.database.client()
            .select("user")
            .await
            .map_err(|e| PrismaError::from(DomainError::DatabaseError(
                format!("Failed to query users: {}", e)
            )))?;

        Ok(users.into_iter().find(|u| u.id == user_id))
    }

    /// Get user by email
    pub async fn get_user_by_email(&self, email: &str) -> PrismaResult<Option<User>> {
        let users: Vec<User> = self.database.client()
            .select("user")
            .await
            .map_err(|e| PrismaError::from(DomainError::DatabaseError(
                format!("Failed to query users: {}", e)
            )))?;

        Ok(users.into_iter().find(|u| u.email == email))
    }

    /// Create a new user in the database
    async fn create_user(&self, user: &User) -> PrismaResult<()> {
        // Use serde_json::json! macro to avoid enum serialization issues
        // This approach manually constructs the JSON to ensure compatibility with SurrealDB
        let user_value = serde_json::json!({
            "id": user.id,
            "email": user.email,
            "password_hash": user.password_hash,
            "role": "user", // Hardcode role as string to avoid enum issues
            "is_active": user.is_active,
            "email_verified": user.email_verified,
            "created_at": user.created_at.to_rfc3339(),
            "updated_at": user.updated_at.to_rfc3339(),
            "last_login": user.last_login.map(|dt| dt.to_rfc3339())
        });

        println!("🔍 About to call SurrealDB create with user_value: {}", user_value);

        let created: Option<User> = self.database.client()
            .create(("user", &user.id))
            .content(user_value)
            .await
            .map_err(|e| {
                println!("🚨 SurrealDB create error: {}", e);
                PrismaError::from(DomainError::DatabaseError(
                    format!("Failed to create user: {}", e)
                ))
            })?;

        if created.is_some() {
            println!("✅ User created successfully in SurrealDB");
            Ok(())
        } else {
            Err(PrismaError::from(DomainError::DatabaseError(
                "Failed to create user: No record returned".to_string()
            )))
        }
    }

    /// Update user in the database
    async fn update_user(&self, user: &User) -> PrismaResult<()> {
        let user_value = serde_json::to_value(user)
            .map_err(|e| PrismaError::from(DomainError::SerializationError(
                format!("Failed to serialize user: {}", e)
            )))?;

        let _updated: Option<User> = self.database.client()
            .update(("user", &user.id))
            .content(user_value)
            .await
            .map_err(|e| PrismaError::from(DomainError::DatabaseError(
                format!("Failed to update user: {}", e)
            )))?;

        Ok(())
    }

    /// Register a device for multi-device support
    async fn register_device(&self, user_id: &str, device_id: &str, device_name: &str, device_type: &str) -> PrismaResult<()> {
        let device = Device {
            id: device_id.to_string(),
            user_id: user_id.to_string(),
            device_name: device_name.to_string(),
            device_type: device_type.to_string(),
            last_active: Utc::now(),
            is_active: true,
            created_at: Utc::now(),
        };

        let device_value = serde_json::to_value(&device)
            .map_err(|e| PrismaError::from(DomainError::SerializationError(
                format!("Failed to serialize device: {}", e)
            )))?;

        let _created: Option<Device> = self.database.client()
            .create(("device", device_id))
            .content(device_value)
            .await
            .map_err(|e| PrismaError::from(DomainError::DatabaseError(
                format!("Failed to create device: {}", e)
            )))?;

        Ok(())
    }

    /// Update device activity timestamp
    async fn update_device_activity(&self, user_id: &str, device_id: &str) -> PrismaResult<()> {
        // Get existing device or create new one
        let devices: Vec<Device> = self.database.client()
            .select("device")
            .await
            .map_err(|e| PrismaError::from(DomainError::DatabaseError(
                format!("Failed to query devices: {}", e)
            )))?;

        if let Some(mut device) = devices.into_iter().find(|d| d.id == device_id && d.user_id == user_id) {
            device.last_active = Utc::now();
            device.is_active = true;

            let device_value = serde_json::to_value(&device)
                .map_err(|e| PrismaError::from(DomainError::SerializationError(
                    format!("Failed to serialize device: {}", e)
                )))?;

            let _updated: Option<Device> = self.database.client()
                .update(("device", device_id))
                .content(device_value)
                .await
                .map_err(|e| PrismaError::from(DomainError::DatabaseError(
                    format!("Failed to update device: {}", e)
                )))?;
        } else {
            // Create new device if it doesn't exist
            self.register_device(user_id, device_id, "Unknown", "Unknown").await?;
        }

        Ok(())
    }

    /// Generate authentication response with JWT tokens
    async fn generate_auth_response(&self, user: &User, device_id: Option<String>) -> PrismaResult<AuthResponse> {
        let now = Utc::now();
        let exp = now + Duration::hours(self.token_expiry_hours);

        let claims = Claims {
            sub: user.id.clone(),
            email: user.email.clone(),
            role: match user.role.as_str() {
                "admin" => UserRole::Admin,
                "guest" => UserRole::Guest,
                _ => UserRole::User,
            },
            exp: exp.timestamp(),
            iat: now.timestamp(),
            device_id,
        };

        let encoding_key = EncodingKey::from_secret(self.jwt_secret.as_ref());
        let header = Header::new(Algorithm::HS256);

        let token = encode(&header, &claims, &encoding_key)
            .map_err(|e| PrismaError::from(DomainError::AuthError(
                format!("Failed to generate token: {}", e)
            )))?;

        // For simplicity, use the same token as refresh token
        // In production, you might want separate refresh token logic
        let refresh_token = token.clone();

        let user_info = UserInfo {
            id: user.id.clone(),
            email: user.email.clone(),
            role: user.role.clone(),
            is_active: user.is_active,
            email_verified: user.email_verified,
            created_at: user.created_at,
            last_login: user.last_login,
        };

        Ok(AuthResponse {
            token,
            refresh_token,
            user: user_info,
            expires_at: exp,
        })
    }

    /// Validate email format
    fn is_valid_email(&self, email: &str) -> bool {
        email.contains('@') && email.contains('.') && email.len() > 5
    }

    /// Validate password strength
    fn is_valid_password(&self, password: &str) -> bool {
        password.len() >= 8
    }

    /// Deactivate user account
    pub async fn deactivate_user(&self, user_id: &str) -> PrismaResult<()> {
        let mut user = self.get_user_by_id(user_id).await?
            .ok_or_else(|| PrismaError::from(DomainError::ValidationError(
                "User not found".to_string()
            )))?;

        user.is_active = false;
        user.updated_at = Utc::now();
        self.update_user(&user).await?;

        info!("Deactivated user: {}", user_id);
        Ok(())
    }

    /// Change user password
    pub async fn change_password(&self, user_id: &str, old_password: &str, new_password: &str) -> PrismaResult<()> {
        let user = self.get_user_by_id(user_id).await?
            .ok_or_else(|| PrismaError::from(DomainError::ValidationError(
                "User not found".to_string()
            )))?;

        // Verify old password
        let password_valid = verify(old_password, &user.password_hash)
            .map_err(|e| PrismaError::from(DomainError::AuthError(
                format!("Password verification failed: {}", e)
            )))?;

        if !password_valid {
            return Err(PrismaError::from(DomainError::AuthError(
                "Invalid current password".to_string()
            )));
        }

        // Validate new password
        if !self.is_valid_password(new_password) {
            return Err(PrismaError::from(DomainError::ValidationError(
                "New password must be at least 8 characters long".to_string()
            )));
        }

        // Hash new password
        let new_password_hash = hash(new_password, DEFAULT_COST)
            .map_err(|e| PrismaError::from(DomainError::AuthError(
                format!("Failed to hash new password: {}", e)
            )))?;

        // Update user
        let mut updated_user = user;
        updated_user.password_hash = new_password_hash;
        updated_user.updated_at = Utc::now();
        self.update_user(&updated_user).await?;

        info!("Changed password for user: {}", user_id);
        Ok(())
    }

    /// Get user devices
    pub async fn get_user_devices(&self, user_id: &str) -> PrismaResult<Vec<Device>> {
        let devices: Vec<Device> = self.database.client()
            .select("device")
            .await
            .map_err(|e| PrismaError::from(DomainError::DatabaseError(
                format!("Failed to query devices: {}", e)
            )))?;

        Ok(devices.into_iter().filter(|d| d.user_id == user_id).collect())
    }
}
