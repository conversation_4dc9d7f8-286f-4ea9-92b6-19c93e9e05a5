// =================================================================================================
// File: /prisma_ai/src/prisma_ui/services/health_service.rs
// =================================================================================================
// Purpose: Health monitoring service for system status diagnostics and telemetry integration.
// This service monitors the health and performance of all PRISMA UI components, backend services,
// and infrastructure dependencies. It integrates with the existing telemetry system to provide
// comprehensive system observability and real-time health status reporting.
// =================================================================================================
// Internal Dependencies:
// - ../publishers/: Health status event publishing for real-time UI health updates
// - ../../telemetry/: Integration with existing telemetry system for health metrics
// - types.rs: Health monitoring data types and component status structures
// - traits.rs: Health service trait definitions and monitoring abstractions
// - cache_service.rs: Health data caching for performance optimization
// =================================================================================================
// External Dependencies:
// - Telemetry System: Integration with existing PRISMA telemetry infrastructure
// - SurrealDB: Health history storage and component status persistence
// - RabbitMQ: Health event publishing and real-time status updates
// - tokio: Async health checks and concurrent component monitoring
// - sysinfo: System resource monitoring (CPU, memory, disk usage)
// =================================================================================================
// Module Interactions:
// - Integrates with existing telemetry system for health metrics collection and reporting
// - Monitors all UI services, backend components, and infrastructure dependencies
// - Publishes health events via RabbitMQ for real-time Flutter UI health dashboards
// - Coordinates with cache service for health data optimization and quick access
// - Provides health context to all UI services for self-monitoring and diagnostics
// =================================================================================================
// Health Monitoring Features:
// - Component Health: Service status monitoring (healthy, degraded, unhealthy)
// - Performance Metrics: Response times, throughput, error rates, and resource usage
// - Infrastructure Monitoring: RabbitMQ, SurrealDB, llama.cpp, and system resources
// - Multi-device Health: Per-device health status and diagnostics across family setup
// - Alerting Integration: Health threshold monitoring and alert generation
// =================================================================================================
// Health Monitoring Architecture:
// ┌─────────────────┐    Health Checks ┌──────────────────────┐    Telemetry     ┌─────────────────┐
// │ System          │ ──────────→      │ Health Monitoring    │ ──────────→      │ Observability   │
// │ Components      │                  │ Service              │                  │ - Telemetry     │
// │ - UI Services   │                  │ ├─Component Monitor  │                  │   System        │
// │ - RabbitMQ      │                  │ ├─Performance Track  │                  │ - Metrics       │
// │ - SurrealDB     │                  │ ├─Infrastructure     │                  │   Collection    │
// │ - llama.cpp     │                  │ │ Health             │                  │ - Dashboards    │
// │ - System Resources│                │ └─Alert Generation   │                  │ - Alerting      │
// └─────────────────┘                  └──────────────────────┘                  └─────────────────┘
//                                               ↓                                          ↓
//                                      ┌──────────────────────┐                  ┌─────────────────┐
//                                      │ Health Dashboard     │                  │ Health Storage  │
//                                      │ - Real-time Status   │ ←────────────────│ - SurrealDB     │
//                                      │ - Performance Graphs │                  │ - Health History│
//                                      │ - Alert Notifications│                  │ - Metrics Data  │
//                                      │ - Multi-device View  │                  │ - Alert Logs    │
//                                      └──────────────────────┘                  └─────────────────┘
// =================================================================================================

use std::collections::HashMap;
use std::sync::Arc;
use std::time::{Duration, SystemTime};
use tokio::sync::RwLock;
use tokio::time::interval;
use tracing::{debug, info, warn};
use serde::{Deserialize, Serialize};

// Import telemetry and error types
use crate::err::PrismaResult;
use crate::telemetry::core::TelemetryCore;
use crate::telemetry::alerts::{HealthAlertManager, HealthAlertConfig};

/// Health status enumeration for UI components
#[derive(Debug, Clone, Copy, PartialEq, Eq, Serialize, Deserialize)]
pub enum UiHealthStatus {
    Healthy,
    Warning,
    Critical,
    Unknown,
}

/// Backend connectivity status for UI services
#[derive(Debug, Clone, Copy, PartialEq, Eq, Serialize, Deserialize)]
pub enum BackendConnectivityStatus {
    Connected,
    Degraded,
    Disconnected,
    Unknown,
}

/// UI component health report
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct UiHealthReport {
    pub service_name: String,
    pub status: UiHealthStatus,
    pub response_time_ms: Option<f64>,
    pub error_rate: Option<f64>,
    pub user_count: Option<u64>,
    pub backend_connectivity: BackendConnectivityStatus,
    pub timestamp: SystemTime,
    pub metrics: Option<HashMap<String, f64>>,
    pub message: Option<String>,
}

/// UI health summary for telemetry integration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct UiHealthSummary {
    pub overall_status: UiHealthStatus,
    pub services: HashMap<String, UiHealthReport>,
    pub performance_metrics: UiPerformanceMetrics,
    pub backend_health: BackendHealthStatus,
    pub timestamp: SystemTime,
    pub alerts: Vec<UiHealthAlert>,
}

/// Performance metrics for UI services
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct UiPerformanceMetrics {
    pub average_response_time_ms: f64,
    pub total_requests: u64,
    pub error_rate: f64,
    pub active_users: u64,
    pub memory_usage_mb: f64,
    pub cpu_usage_percent: f64,
}

/// Backend health status aggregation
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct BackendHealthStatus {
    pub engine_connectivity: BackendConnectivityStatus,
    pub rabbitmq_connectivity: BackendConnectivityStatus,
    pub surrealdb_connectivity: BackendConnectivityStatus,
    pub llama_cpp_connectivity: BackendConnectivityStatus,
}

/// UI health alerts
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum UiHealthAlert {
    ServiceDegraded { service: String, impact: String },
    BackendDisconnected { backend: String, duration_secs: u64 },
    PerformanceIssue { metric: String, threshold_exceeded: f64 },
    UserImpact { description: String, affected_users: u64 },
}

/// Configuration for the UI health service
#[derive(Debug, Clone)]
pub struct UiHealthServiceConfig {
    /// How often to check UI service health (in seconds)
    pub health_check_interval_secs: u64,
    /// Performance sampling rate (0.0 to 1.0)
    pub performance_sampling_rate: f64,
    /// Backend connectivity timeout (in seconds)
    pub backend_timeout_secs: u64,
    /// Whether to enable user metrics collection
    pub enable_user_metrics: bool,
    /// Whether to enable telemetry publishing
    pub enable_telemetry_publishing: bool,
    /// How often to publish to telemetry (in seconds)
    pub telemetry_publish_interval_secs: u64,
}

impl Default for UiHealthServiceConfig {
    fn default() -> Self {
        Self {
            health_check_interval_secs: 15,
            performance_sampling_rate: 0.1, // 10%
            backend_timeout_secs: 5,
            enable_user_metrics: true,
            enable_telemetry_publishing: true,
            telemetry_publish_interval_secs: 60,
        }
    }
}

/// UI Health Service for monitoring UI components and coordinating with Engine Health Service
pub struct UiHealthService {
    /// Configuration for the health service
    config: UiHealthServiceConfig,
    /// UI service health reports storage
    service_reports: Arc<RwLock<HashMap<String, UiHealthReport>>>,
    /// Reference to telemetry system for health data publishing
    telemetry: Option<Arc<TelemetryCore>>,
    /// Health alert manager for processing health-specific alerts
    alert_manager: Option<HealthAlertManager>,
    /// Background task handles
    task_handles: Vec<tokio::task::JoinHandle<()>>,
    /// Whether the service is currently running
    is_running: Arc<RwLock<bool>>,
}

impl UiHealthService {
    /// Create a new UI health service
    pub fn new(config: UiHealthServiceConfig) -> Self {
        Self {
            config,
            service_reports: Arc::new(RwLock::new(HashMap::new())),
            telemetry: None,
            alert_manager: None,
            task_handles: Vec::new(),
            is_running: Arc::new(RwLock::new(false)),
        }
    }

    /// Initialize the UI health service with telemetry integration
    pub async fn initialize(&mut self, telemetry: Option<Arc<TelemetryCore>>) -> PrismaResult<()> {
        info!("Initializing UiHealthService");

        self.telemetry = telemetry.clone();

        // Initialize health alert manager if telemetry is available
        if let Some(telemetry_core) = telemetry {
            let alert_config = HealthAlertConfig::default();
            self.alert_manager = Some(HealthAlertManager::with_config(telemetry_core, alert_config));
            info!("UI health alert manager initialized");
        }

        // Start background health monitoring and telemetry publishing tasks
        self.start_background_tasks().await?;

        let mut running = self.is_running.write().await;
        *running = true;

        info!("UiHealthService initialized successfully");
        Ok(())
    }

    /// Start background tasks for health monitoring and telemetry publishing
    async fn start_background_tasks(&mut self) -> PrismaResult<()> {
        // Health monitoring task
        if self.config.health_check_interval_secs > 0 {
            let reports_clone = Arc::clone(&self.service_reports);
            let interval_secs = self.config.health_check_interval_secs;
            let backend_timeout = self.config.backend_timeout_secs;

            let monitoring_handle = tokio::spawn(async move {
                let mut interval = interval(Duration::from_secs(interval_secs));

                loop {
                    interval.tick().await;

                    // Perform health checks on UI services
                    let health_checks = Self::perform_health_checks(backend_timeout).await;

                    // Update reports
                    let mut reports = reports_clone.write().await;
                    for (service_name, report) in health_checks {
                        reports.insert(service_name, report);
                    }

                    debug!("Completed UI health checks for {} services", reports.len());
                }
            });

            self.task_handles.push(monitoring_handle);
        }

        // Telemetry publishing task
        if self.config.enable_telemetry_publishing && self.config.telemetry_publish_interval_secs > 0 {
            let reports_clone = Arc::clone(&self.service_reports);
            let telemetry_clone = self.telemetry.clone();
            let interval_secs = self.config.telemetry_publish_interval_secs;

            let telemetry_handle = tokio::spawn(async move {
                let mut interval = interval(Duration::from_secs(interval_secs));

                loop {
                    interval.tick().await;

                    if let Some(telemetry) = &telemetry_clone {
                        let reports = reports_clone.read().await;

                        // Create UI health summary for telemetry
                        let summary = Self::create_health_summary(&reports).await;

                        // Publish to telemetry system
                        if let Err(e) = Self::publish_to_telemetry(telemetry, &summary).await {
                            warn!("Failed to publish UI health data to telemetry: {}", e);
                        } else {
                            debug!("Published UI health data to telemetry for {} services", reports.len());
                        }
                    }
                }
            });

            self.task_handles.push(telemetry_handle);
        }

        Ok(())
    }

    /// Perform health checks on UI services
    async fn perform_health_checks(backend_timeout_secs: u64) -> HashMap<String, UiHealthReport> {
        let mut reports = HashMap::new();
        let timestamp = SystemTime::now();

        // Check chat service health
        let chat_health = Self::check_chat_service_health(backend_timeout_secs).await;
        reports.insert("chat_service".to_string(), UiHealthReport {
            service_name: "chat_service".to_string(),
            status: chat_health.0,
            response_time_ms: chat_health.1,
            error_rate: Some(0.0), // TODO: Implement actual error rate tracking
            user_count: Some(0), // TODO: Implement actual user count tracking
            backend_connectivity: chat_health.2,
            timestamp,
            metrics: None,
            message: chat_health.3,
        });

        // Check agent service health
        let agent_health = Self::check_agent_service_health(backend_timeout_secs).await;
        reports.insert("agent_service".to_string(), UiHealthReport {
            service_name: "agent_service".to_string(),
            status: agent_health.0,
            response_time_ms: agent_health.1,
            error_rate: Some(0.0),
            user_count: Some(0),
            backend_connectivity: agent_health.2,
            timestamp,
            metrics: None,
            message: agent_health.3,
        });

        // Check project service health
        let project_health = Self::check_project_service_health(backend_timeout_secs).await;
        reports.insert("project_service".to_string(), UiHealthReport {
            service_name: "project_service".to_string(),
            status: project_health.0,
            response_time_ms: project_health.1,
            error_rate: Some(0.0),
            user_count: Some(0),
            backend_connectivity: project_health.2,
            timestamp,
            metrics: None,
            message: project_health.3,
        });

        reports
    }

    /// Check chat service health
    async fn check_chat_service_health(_timeout_secs: u64) -> (UiHealthStatus, Option<f64>, BackendConnectivityStatus, Option<String>) {
        // TODO: Implement actual chat service health check
        (UiHealthStatus::Healthy, Some(50.0), BackendConnectivityStatus::Connected, Some("Chat service operational".to_string()))
    }

    /// Check agent service health
    async fn check_agent_service_health(_timeout_secs: u64) -> (UiHealthStatus, Option<f64>, BackendConnectivityStatus, Option<String>) {
        // TODO: Implement actual agent service health check
        (UiHealthStatus::Healthy, Some(75.0), BackendConnectivityStatus::Connected, Some("Agent service operational".to_string()))
    }

    /// Check project service health
    async fn check_project_service_health(_timeout_secs: u64) -> (UiHealthStatus, Option<f64>, BackendConnectivityStatus, Option<String>) {
        // TODO: Implement actual project service health check
        (UiHealthStatus::Healthy, Some(60.0), BackendConnectivityStatus::Connected, Some("Project service operational".to_string()))
    }

    /// Create health summary from service reports
    async fn create_health_summary(reports: &HashMap<String, UiHealthReport>) -> UiHealthSummary {
        let timestamp = SystemTime::now();

        // Calculate overall status
        let overall_status = if reports.is_empty() {
            UiHealthStatus::Unknown
        } else {
            let mut has_critical = false;
            let mut has_warning = false;

            for report in reports.values() {
                match report.status {
                    UiHealthStatus::Critical => has_critical = true,
                    UiHealthStatus::Warning => has_warning = true,
                    _ => {}
                }
            }

            if has_critical {
                UiHealthStatus::Critical
            } else if has_warning {
                UiHealthStatus::Warning
            } else {
                UiHealthStatus::Healthy
            }
        };

        // Calculate performance metrics
        let performance_metrics = Self::calculate_performance_metrics(reports).await;

        // Calculate backend health
        let backend_health = Self::calculate_backend_health(reports).await;

        // Generate alerts
        let alerts = Self::generate_alerts(reports).await;

        UiHealthSummary {
            overall_status,
            services: reports.clone(),
            performance_metrics,
            backend_health,
            timestamp,
            alerts,
        }
    }

    /// Calculate performance metrics from service reports
    async fn calculate_performance_metrics(reports: &HashMap<String, UiHealthReport>) -> UiPerformanceMetrics {
        let mut total_response_time = 0.0;
        let mut response_time_count = 0;
        let total_requests = 0;
        let mut total_errors = 0.0;
        let mut total_users = 0;

        for report in reports.values() {
            if let Some(response_time) = report.response_time_ms {
                total_response_time += response_time;
                response_time_count += 1;
            }

            if let Some(error_rate) = report.error_rate {
                total_errors += error_rate;
            }

            if let Some(user_count) = report.user_count {
                total_users += user_count;
            }
        }

        let average_response_time = if response_time_count > 0 {
            total_response_time / response_time_count as f64
        } else {
            0.0
        };

        let error_rate = if !reports.is_empty() {
            total_errors / reports.len() as f64
        } else {
            0.0
        };

        UiPerformanceMetrics {
            average_response_time_ms: average_response_time,
            total_requests,
            error_rate,
            active_users: total_users,
            memory_usage_mb: 0.0, // TODO: Implement actual memory tracking
            cpu_usage_percent: 0.0, // TODO: Implement actual CPU tracking
        }
    }

    /// Calculate backend health status
    async fn calculate_backend_health(reports: &HashMap<String, UiHealthReport>) -> BackendHealthStatus {
        let mut engine_status = BackendConnectivityStatus::Unknown;
        let rabbitmq_status = BackendConnectivityStatus::Unknown;
        let surrealdb_status = BackendConnectivityStatus::Unknown;
        let llama_cpp_status = BackendConnectivityStatus::Unknown;

        // Aggregate backend connectivity from service reports
        for report in reports.values() {
            match report.backend_connectivity {
                BackendConnectivityStatus::Connected => {
                    engine_status = BackendConnectivityStatus::Connected;
                    // TODO: Differentiate between different backend services
                }
                BackendConnectivityStatus::Degraded => {
                    if engine_status != BackendConnectivityStatus::Connected {
                        engine_status = BackendConnectivityStatus::Degraded;
                    }
                }
                BackendConnectivityStatus::Disconnected => {
                    engine_status = BackendConnectivityStatus::Disconnected;
                }
                _ => {}
            }
        }

        BackendHealthStatus {
            engine_connectivity: engine_status,
            rabbitmq_connectivity: rabbitmq_status,
            surrealdb_connectivity: surrealdb_status,
            llama_cpp_connectivity: llama_cpp_status,
        }
    }

    /// Generate health alerts from service reports
    async fn generate_alerts(reports: &HashMap<String, UiHealthReport>) -> Vec<UiHealthAlert> {
        let mut alerts = Vec::new();

        for report in reports.values() {
            match report.status {
                UiHealthStatus::Critical => {
                    alerts.push(UiHealthAlert::ServiceDegraded {
                        service: report.service_name.clone(),
                        impact: "Service is critical".to_string(),
                    });
                }
                UiHealthStatus::Warning => {
                    alerts.push(UiHealthAlert::ServiceDegraded {
                        service: report.service_name.clone(),
                        impact: "Service performance degraded".to_string(),
                    });
                }
                _ => {}
            }

            if report.backend_connectivity == BackendConnectivityStatus::Disconnected {
                alerts.push(UiHealthAlert::BackendDisconnected {
                    backend: "engine".to_string(),
                    duration_secs: 0, // TODO: Track actual disconnection duration
                });
            }

            if let Some(response_time) = report.response_time_ms {
                if response_time > 1000.0 { // Alert if response time > 1 second
                    alerts.push(UiHealthAlert::PerformanceIssue {
                        metric: "response_time".to_string(),
                        threshold_exceeded: response_time,
                    });
                }
            }
        }

        alerts
    }

    /// Publish health data to telemetry system
    async fn publish_to_telemetry(telemetry: &Arc<TelemetryCore>, summary: &UiHealthSummary) -> PrismaResult<()> {
        debug!("Publishing UI health summary to telemetry: overall_status={:?}, services={}",
               summary.overall_status, summary.services.len());

        // Convert UI health summary to telemetry metrics
        let mut telemetry_metrics = std::collections::HashMap::new();

        // Overall UI health status as numeric value
        let overall_status_value = match summary.overall_status {
            UiHealthStatus::Healthy => 1.0,
            UiHealthStatus::Warning => 0.6,
            UiHealthStatus::Critical => 0.2,
            UiHealthStatus::Unknown => 0.0,
        };
        telemetry_metrics.insert("prisma_ui_health_overall".to_string(), overall_status_value);

        // UI service health metrics
        for (service_name, report) in &summary.services {
            let service_status_value = match report.status {
                UiHealthStatus::Healthy => 1.0,
                UiHealthStatus::Warning => 0.6,
                UiHealthStatus::Critical => 0.2,
                UiHealthStatus::Unknown => 0.0,
            };

            let metric_name = format!("prisma_ui_service_health_{}", service_name);
            telemetry_metrics.insert(metric_name, service_status_value);

            // Include service-specific metrics
            if let Some(response_time) = report.response_time_ms {
                let response_time_metric = format!("prisma_ui_service_response_time_{}", service_name);
                telemetry_metrics.insert(response_time_metric, response_time);
            }

            if let Some(error_rate) = report.error_rate {
                let error_rate_metric = format!("prisma_ui_service_error_rate_{}", service_name);
                telemetry_metrics.insert(error_rate_metric, error_rate);
            }

            if let Some(user_count) = report.user_count {
                let user_count_metric = format!("prisma_ui_service_users_{}", service_name);
                telemetry_metrics.insert(user_count_metric, user_count as f64);
            }

            // Backend connectivity metrics
            let connectivity_value = match report.backend_connectivity {
                BackendConnectivityStatus::Connected => 1.0,
                BackendConnectivityStatus::Degraded => 0.6,
                BackendConnectivityStatus::Disconnected => 0.0,
                BackendConnectivityStatus::Unknown => 0.0,
            };
            let connectivity_metric = format!("prisma_ui_backend_connectivity_{}", service_name);
            telemetry_metrics.insert(connectivity_metric, connectivity_value);
        }

        // Performance metrics
        telemetry_metrics.insert("prisma_ui_avg_response_time".to_string(), summary.performance_metrics.average_response_time_ms);
        telemetry_metrics.insert("prisma_ui_total_requests".to_string(), summary.performance_metrics.total_requests as f64);
        telemetry_metrics.insert("prisma_ui_error_rate".to_string(), summary.performance_metrics.error_rate);
        telemetry_metrics.insert("prisma_ui_active_users".to_string(), summary.performance_metrics.active_users as f64);

        // Backend health aggregation
        let engine_connectivity_value = match summary.backend_health.engine_connectivity {
            BackendConnectivityStatus::Connected => 1.0,
            BackendConnectivityStatus::Degraded => 0.6,
            BackendConnectivityStatus::Disconnected => 0.0,
            BackendConnectivityStatus::Unknown => 0.0,
        };
        telemetry_metrics.insert("prisma_ui_engine_connectivity".to_string(), engine_connectivity_value);

        // Alert count metric
        telemetry_metrics.insert("prisma_ui_alerts_total".to_string(), summary.alerts.len() as f64);

        // Create telemetry span for UI health data
        let attributes = vec![
            opentelemetry::KeyValue::new("service.name", "prisma_ui"),
            opentelemetry::KeyValue::new("health.overall_status", format!("{:?}", summary.overall_status)),
            opentelemetry::KeyValue::new("health.services_count", summary.services.len() as i64),
            opentelemetry::KeyValue::new("health.alerts_count", summary.alerts.len() as i64),
            opentelemetry::KeyValue::new("health.active_users", summary.performance_metrics.active_users as i64),
        ];

        let _span = telemetry.create_span("ui_health_report".to_string(), attributes);

        // Log structured UI health data for Loki ingestion
        tracing::info!(
            target: "ui_health_telemetry",
            overall_status = ?summary.overall_status,
            services_count = summary.services.len(),
            alerts_count = summary.alerts.len(),
            active_users = summary.performance_metrics.active_users,
            avg_response_time = summary.performance_metrics.average_response_time_ms,
            error_rate = summary.performance_metrics.error_rate,
            timestamp = ?summary.timestamp,
            "UI health telemetry data published"
        );

        // Log individual service health for detailed monitoring
        for (service_name, report) in &summary.services {
            tracing::info!(
                target: "ui_service_health_telemetry",
                service = service_name,
                status = ?report.status,
                response_time_ms = ?report.response_time_ms,
                error_rate = ?report.error_rate,
                user_count = ?report.user_count,
                backend_connectivity = ?report.backend_connectivity,
                message = ?report.message,
                timestamp = ?report.timestamp,
                "UI service health telemetry data"
            );
        }

        Ok(())
    }

    /// Report health status from a UI service
    pub async fn report_service_health(&self, report: UiHealthReport) -> PrismaResult<()> {
        debug!("Received health report from UI service: {} - Status: {:?}",
               report.service_name, report.status);

        let mut reports = self.service_reports.write().await;
        reports.insert(report.service_name.clone(), report);

        Ok(())
    }

    /// Get the current UI health summary
    pub async fn get_health_summary(&self) -> PrismaResult<UiHealthSummary> {
        let reports = self.service_reports.read().await;
        let summary = Self::create_health_summary(&reports).await;

        // Process health alerts if alert manager is available
        if let Some(ref alert_manager) = self.alert_manager {
            if let Err(e) = alert_manager.process_ui_health(&summary).await {
                warn!("Failed to process UI health alerts: {}", e);
            }
        }

        Ok(summary)
    }

    /// Shutdown the UI health service
    pub async fn shutdown(&mut self) -> PrismaResult<()> {
        info!("Shutting down UiHealthService");

        let mut running = self.is_running.write().await;
        *running = false;

        // Cancel all background tasks
        for handle in self.task_handles.drain(..) {
            handle.abort();
        }

        info!("UiHealthService shutdown completed");
        Ok(())
    }
}

/// Helper function to create a UI health report
pub fn create_ui_health_report(
    service_name: &str,
    status: UiHealthStatus,
    message: Option<String>,
    metrics: Option<HashMap<String, f64>>,
) -> UiHealthReport {
    UiHealthReport {
        service_name: service_name.to_string(),
        status,
        response_time_ms: None,
        error_rate: None,
        user_count: None,
        backend_connectivity: BackendConnectivityStatus::Unknown,
        timestamp: SystemTime::now(),
        metrics,
        message,
    }
}
