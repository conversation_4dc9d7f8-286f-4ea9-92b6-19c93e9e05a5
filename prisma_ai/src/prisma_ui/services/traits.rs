// =================================================================================================
// File: /prisma_ai/src/prisma_ui/services/traits.rs
// =================================================================================================
// Purpose: Trait definitions and abstractions for UI service layer components and operations.
// This file defines the core traits that establish contracts for UI services, enabling modular
// design, testability, and flexible implementations across chat services, agent services,
// project creation, participant management, and MCP integration operations.
// =================================================================================================
// Internal Dependencies:
// - types.rs: UI service data types used in trait method signatures
// - basic_agent_service.rs: Agent service types for trait implementations
// - chat_service.rs: Chat service types for trait implementations
// - new_project/: Project service types for trait implementations
// - participants/: Participant service types for trait implementations
// - mcp_service.rs: MCP service types for trait implementations
// =================================================================================================
// External Dependencies:
// - async_trait: Enables async methods in traits for UI service operations
// - std::future::Future: For async trait method return types and operations
// - serde: Serialization traits for service data persistence and communication
// - std::sync::Arc: Shared ownership traits for service lifecycle management
// =================================================================================================
// Module Interactions:
// - Implemented by all UI service layer components for business logic abstraction
// - Used by service coordination system for unified service management
// - Enables dependency injection and testing with mock service implementations
// - Provides contracts for service lifecycle management and operation coordination
// - Facilitates integration with backend systems through standardized interfaces
// =================================================================================================
// Trait Categories:
// - Service Management: Core service lifecycle and operation management abstractions
// - Communication Traits: Interfaces for service communication and event handling
// - Configuration Traits: Abstractions for service configuration and setup management
// - Coordination Traits: Interfaces for inter-service coordination and workflow management
// - Integration Traits: Contracts for service integration with backend systems
// =================================================================================================
// UI Service Trait Architecture:
// ┌─────────────────┐    Implementation  ┌──────────────────────┐    Abstraction    ┌─────────────────┐
// │ Concrete        │ ──────────→        │ UI Service Traits    │ ──────────→       │ Service Layer   │
// │ Services        │                    │ ├─ServiceManager     │                   │ - Unified API   │
// │ - Chat Service  │                    │ ├─Communicator       │                   │ - Testability   │
// │ - Agent Service │                    │ ├─Configurator       │                   │ - Modularity    │
// │ - Project Svc   │                    │ ├─Coordinator        │                   │ - Flexibility   │
// │ - MCP Service   │                    │ └─Integrator         │                   │ - Coordination  │
// └─────────────────┘                    └──────────────────────┘                   └─────────────────┘
// =================================================================================================

use std::collections::HashMap;
use std::sync::Arc;
use std::time::Duration;
use async_trait::async_trait;
use serde::{Serialize, Deserialize};

// Import error handling
use crate::err::PrismaResult;

// Import service types
use super::types::*;

/// Core trait for UI service lifecycle management
#[async_trait]
pub trait UiService: Send + Sync {
    /// Get the service identifier
    fn service_id(&self) -> &ServiceId;

    /// Initialize the service with configuration
    async fn initialize(&mut self, context: ServiceContext) -> PrismaResult<()>;

    /// Start the service
    async fn start(&mut self) -> PrismaResult<()>;

    /// Stop the service gracefully
    async fn stop(&mut self) -> PrismaResult<()>;

    /// Pause the service (temporary stop)
    async fn pause(&mut self) -> PrismaResult<()>;

    /// Resume the service from paused state
    async fn resume(&mut self) -> PrismaResult<()>;

    /// Get current service status
    fn status(&self) -> ServiceStatus;

    /// Get service health information
    async fn health_check(&self) -> PrismaResult<ServiceHealth>;

    /// Get service metrics
    async fn get_metrics(&self) -> PrismaResult<ServiceMetrics>;

    /// Handle service configuration updates
    async fn update_config(&mut self, config: ServiceConfig) -> PrismaResult<()>;
}

/// Trait for services that handle user authentication
#[async_trait]
pub trait AuthenticationProvider: Send + Sync {
    /// Authenticate user with credentials
    async fn authenticate(&self, email: &str, password: &str) -> PrismaResult<AuthenticationResult>;

    /// Validate a JWT token
    async fn validate_token(&self, token: &str) -> PrismaResult<TokenValidationResult>;

    /// Refresh an authentication token
    async fn refresh_token(&self, refresh_token: &str) -> PrismaResult<AuthenticationResult>;

    /// Register a new user
    async fn register_user(&self, registration: UserRegistrationRequest) -> PrismaResult<UserId>;

    /// Change user password
    async fn change_password(&self, user_id: &UserId, old_password: &str, new_password: &str) -> PrismaResult<()>;

    /// Deactivate user account
    async fn deactivate_user(&self, user_id: &UserId) -> PrismaResult<()>;
}

/// Trait for services that manage user sessions
#[async_trait]
pub trait SessionManager: Send + Sync {
    /// Create a new session
    async fn create_session(&self, request: CreateSessionRequest) -> PrismaResult<SessionInfo>;

    /// Get session information
    async fn get_session(&self, session_id: &SessionId) -> PrismaResult<Option<SessionInfo>>;

    /// Update session activity
    async fn update_activity(&self, session_id: &SessionId) -> PrismaResult<()>;

    /// Update session preferences
    async fn update_preferences(&self, session_id: &SessionId, preferences: UserPreferences) -> PrismaResult<()>;

    /// Update session context
    async fn update_context(&self, session_id: &SessionId, context: SessionContext) -> PrismaResult<()>;

    /// Terminate session
    async fn terminate_session(&self, session_id: &SessionId) -> PrismaResult<()>;

    /// Get all active sessions for a user
    async fn get_user_sessions(&self, user_id: &UserId) -> PrismaResult<Vec<SessionInfo>>;

    /// Clean up expired sessions
    async fn cleanup_expired_sessions(&self) -> PrismaResult<u64>;
}

/// Trait for services that provide caching functionality
#[async_trait]
pub trait CacheProvider: Send + Sync {
    /// Set a value in the cache
    async fn set<T>(&self, namespace: &str, key: &str, value: &T, ttl: Option<Duration>) -> PrismaResult<()>
    where
        T: Serialize + Send + Sync;

    /// Get a value from the cache
    async fn get<T>(&self, namespace: &str, key: &str) -> PrismaResult<Option<T>>
    where
        T: for<'de> Deserialize<'de> + Send + Sync;

    /// Delete a key from the cache
    async fn delete(&self, namespace: &str, key: &str) -> PrismaResult<bool>;

    /// Check if a key exists
    async fn exists(&self, namespace: &str, key: &str) -> PrismaResult<bool>;

    /// Set TTL for an existing key
    async fn expire(&self, namespace: &str, key: &str, ttl: Duration) -> PrismaResult<bool>;

    /// Invalidate cache keys by pattern
    async fn invalidate_pattern(&self, pattern: &str) -> PrismaResult<u64>;

    /// Get cache statistics
    async fn get_stats(&self) -> PrismaResult<CacheStatistics>;

    /// Health check for cache service
    async fn health_check(&self) -> PrismaResult<bool>;
}

/// Trait for services that provide health monitoring
#[async_trait]
pub trait HealthMonitor: Send + Sync {
    /// Get overall system health
    async fn get_system_health(&self) -> PrismaResult<SystemHealthReport>;

    /// Get health for a specific service
    async fn get_service_health(&self, service_id: &ServiceId) -> PrismaResult<ServiceHealth>;

    /// Register a service for health monitoring
    async fn register_service(&self, service_id: ServiceId, config: HealthCheckConfig) -> PrismaResult<()>;

    /// Unregister a service from health monitoring
    async fn unregister_service(&self, service_id: &ServiceId) -> PrismaResult<()>;

    /// Update service health status
    async fn update_health_status(&self, service_id: &ServiceId, health: ServiceHealth) -> PrismaResult<()>;

    /// Get health history for a service
    async fn get_health_history(&self, service_id: &ServiceId, duration: Duration) -> PrismaResult<Vec<ServiceHealth>>;
}

/// Trait for services that handle events and messaging
#[async_trait]
pub trait EventHandler: Send + Sync {
    /// Handle a service event
    async fn handle_event(&self, event: ServiceEvent) -> PrismaResult<()>;

    /// Publish an event
    async fn publish_event(&self, event: ServiceEvent) -> PrismaResult<()>;

    /// Subscribe to events of a specific type
    async fn subscribe(&self, event_types: Vec<ServiceEventType>) -> PrismaResult<()>;

    /// Unsubscribe from events
    async fn unsubscribe(&self, event_types: Vec<ServiceEventType>) -> PrismaResult<()>;
}

/// Trait for services that can be configured dynamically
#[async_trait]
pub trait Configurable: Send + Sync {
    /// Get current configuration
    async fn get_config(&self) -> PrismaResult<ServiceConfig>;

    /// Update configuration
    async fn update_config(&mut self, config: ServiceConfig) -> PrismaResult<()>;

    /// Validate configuration
    async fn validate_config(&self, config: &ServiceConfig) -> PrismaResult<bool>;

    /// Reset to default configuration
    async fn reset_config(&mut self) -> PrismaResult<()>;
}

/// Trait for services that coordinate with other services
#[async_trait]
pub trait ServiceCoordinator: Send + Sync {
    /// Register a service dependency
    async fn register_dependency(&self, dependency: ServiceDependency) -> PrismaResult<()>;

    /// Check if all dependencies are healthy
    async fn check_dependencies(&self) -> PrismaResult<Vec<ServiceDependency>>;

    /// Send a message to another service
    async fn send_message(&self, message: ServiceMessage) -> PrismaResult<ServiceResponse<serde_json::Value>>;

    /// Handle incoming messages
    async fn handle_message(&self, message: ServiceMessage) -> PrismaResult<ServiceResponse<serde_json::Value>>;

    /// Get service registry information
    async fn get_service_registry(&self) -> PrismaResult<Vec<ServiceRegistryEntry>>;
}

/// Trait for services that provide metrics and monitoring
#[async_trait]
pub trait MetricsProvider: Send + Sync {
    /// Get current metrics
    async fn get_metrics(&self) -> PrismaResult<ServiceMetrics>;

    /// Get performance statistics
    async fn get_performance_stats(&self) -> PrismaResult<ServicePerformanceStats>;

    /// Record a metric
    async fn record_metric(&self, name: &str, value: f64, tags: HashMap<String, String>) -> PrismaResult<()>;

    /// Increment a counter
    async fn increment_counter(&self, name: &str, tags: HashMap<String, String>) -> PrismaResult<()>;

    /// Record timing information
    async fn record_timing(&self, name: &str, duration: Duration, tags: HashMap<String, String>) -> PrismaResult<()>;
}

// Supporting types for the traits

/// Authentication result
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AuthenticationResult {
    pub user_id: UserId,
    pub token: String,
    pub refresh_token: String,
    pub expires_at: chrono::DateTime<chrono::Utc>,
    pub user_info: UserInfo,
}

/// Token validation result
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct TokenValidationResult {
    pub valid: bool,
    pub user_id: Option<UserId>,
    pub expires_at: Option<chrono::DateTime<chrono::Utc>>,
    pub claims: HashMap<String, serde_json::Value>,
}

/// User registration request
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct UserRegistrationRequest {
    pub email: String,
    pub password: String,
    pub device_id: Option<DeviceId>,
    pub metadata: HashMap<String, serde_json::Value>,
}

/// User information
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct UserInfo {
    pub id: UserId,
    pub email: String,
    pub role: String,
    pub is_active: bool,
    pub created_at: chrono::DateTime<chrono::Utc>,
    pub last_login: Option<chrono::DateTime<chrono::Utc>>,
}

/// Session information
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SessionInfo {
    pub id: SessionId,
    pub user_id: UserId,
    pub device_id: DeviceId,
    pub is_active: bool,
    pub created_at: chrono::DateTime<chrono::Utc>,
    pub last_activity: chrono::DateTime<chrono::Utc>,
    pub expires_at: chrono::DateTime<chrono::Utc>,
}

/// Create session request
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CreateSessionRequest {
    pub user_id: UserId,
    pub device_id: DeviceId,
    pub preferences: Option<UserPreferences>,
}

/// User preferences
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct UserPreferences {
    pub theme: String,
    pub language: String,
    pub notifications_enabled: bool,
    pub custom_settings: HashMap<String, serde_json::Value>,
}

/// Session context
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SessionContext {
    pub active_chat_id: Option<ChatId>,
    pub active_project_id: Option<ProjectId>,
    pub active_agent_id: Option<AgentId>,
    pub last_location: String,
    pub metadata: HashMap<String, serde_json::Value>,
}

/// Cache statistics
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CacheStatistics {
    pub total_keys: u64,
    pub hits: u64,
    pub misses: u64,
    pub hit_rate: f64,
    pub memory_usage: u64,
    pub expired_keys: u64,
}

/// System health report
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SystemHealthReport {
    pub overall_status: HealthStatus,
    pub services: Vec<ServiceHealth>,
    pub dependencies: Vec<ServiceDependency>,
    pub timestamp: chrono::DateTime<chrono::Utc>,
}

/// Health check configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct HealthCheckConfig {
    pub interval_seconds: u64,
    pub timeout_seconds: u64,
    pub failure_threshold: u32,
    pub success_threshold: u32,
    pub enabled: bool,
}