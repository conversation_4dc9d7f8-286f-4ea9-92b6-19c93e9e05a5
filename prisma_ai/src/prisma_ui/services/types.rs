// =================================================================================================
// File: /prisma_ai/src/prisma_ui/services/types.rs
// =================================================================================================
// Purpose: Core data types and structures for UI service layer operations and communication.
// This file defines the fundamental data types used throughout the UI service layer for
// representing service state, request/response structures, configuration data, and
// communication structures between UI services and backend systems.
// =================================================================================================
// Internal Dependencies:
// - ../types.rs: Core UI data types for service layer integration
// - ../publishers/types.rs: Publisher types for service event structures
// - ../../prisma/types.rs: Core PRISMA system types for service integration
// - basic_agent_service.rs: Agent service specific data types
// - chat_service.rs: Chat service specific data types
// =================================================================================================
// External Dependencies:
// - serde: Serialization and deserialization for service data persistence and communication
// - chrono: Date and time handling for service timestamps and operation tracking
// - uuid: Unique identifier generation for services, operations, and entities
// - std::collections: HashMap and other collection types for service data organization
// =================================================================================================
// Module Interactions:
// - Used by all UI services for data structure definitions and type safety
// - Shared with service coordination system for unified data management
// - Integrated with backend systems for type-safe service communication
// - Provides type safety for service-to-service communication and coordination
// - Defines configuration structures for service setup and operation management
// =================================================================================================
// Type Categories:
// - Service State Types: Structures representing current service state and operation status
// - Request/Response Types: Structures for service communication and operation coordination
// - Configuration Types: Data structures for service configuration and setup management
// - Event Types: Structures for service events and inter-service communication
// - Integration Types: Types for service integration with backend systems and coordination
// =================================================================================================
// UI Service Data Architecture:
// ┌─────────────────┐    Data Flow      ┌──────────────────────┐    Persistence    ┌─────────────────┐
// │ Flutter UI      │ ←─────────────→   │ Service Layer Types  │ ←───────────→     │ Storage Systems │
// │ - Service       │                   │ ├─State Structures   │                   │ - SurrealDB     │
// │   Requests      │                   │ ├─Request/Response   │                   │ - Configuration │
// │ - Configuration │                   │ ├─Configuration Data │                   │ - Backend       │
// │ - Event Data    │                   │ ├─Event Structures   │                   │   Systems       │
// └─────────────────┘                   │ └─Integration Types  │                   └─────────────────┘
//                                       └──────────────────────┘
// =================================================================================================

use std::collections::HashMap;
use std::sync::Arc;
use std::time::{Duration, SystemTime};
use serde::{Serialize, Deserialize};
use chrono::{DateTime, Utc};
use uuid::Uuid;

// Import error handling
use crate::err::PrismaResult;

// Import storage and configuration
use crate::storage::Database;

/// Service identifier type for unique service identification
pub type ServiceId = String;

/// User identifier type for user management
pub type UserId = String;

/// Session identifier type for session management
pub type SessionId = String;

/// Device identifier type for multi-device support
pub type DeviceId = String;

/// Project identifier type for project management
pub type ProjectId = String;

/// Chat identifier type for conversation management
pub type ChatId = String;

/// Agent identifier type for agent management
pub type AgentId = String;

/// Service status enumeration
#[derive(Debug, Clone, Copy, PartialEq, Eq, Serialize, Deserialize)]
pub enum ServiceStatus {
    /// Service is not initialized
    NotInitialized,
    /// Service is initializing
    Initializing,
    /// Service is running normally
    Running,
    /// Service is paused
    Paused,
    /// Service is stopping
    Stopping,
    /// Service is stopped
    Stopped,
    /// Service has encountered an error
    Error,
}

/// Service health status
#[derive(Debug, Clone, Copy, PartialEq, Eq, Serialize, Deserialize)]
pub enum HealthStatus {
    /// Service is healthy
    Healthy,
    /// Service has warnings but is functional
    Warning,
    /// Service is in critical state
    Critical,
    /// Service health is unknown
    Unknown,
}

/// Service health information
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ServiceHealth {
    pub service_id: ServiceId,
    pub status: HealthStatus,
    pub response_time_ms: Option<u64>,
    pub error_rate: Option<f64>,
    pub last_check: DateTime<Utc>,
    pub message: Option<String>,
    pub metrics: HashMap<String, f64>,
}

/// Service configuration base structure
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ServiceConfig {
    pub service_id: ServiceId,
    pub enabled: bool,
    pub timeout_seconds: u64,
    pub retry_attempts: u32,
    pub health_check_interval_seconds: u64,
    pub custom_settings: HashMap<String, serde_json::Value>,
}

/// Service initialization context
#[derive(Debug, Clone)]
pub struct ServiceContext {
    pub database: Arc<Database>,
    pub service_id: ServiceId,
    pub config: ServiceConfig,
    pub startup_time: DateTime<Utc>,
}

/// Service operation result with metadata
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ServiceResult<T> {
    pub data: T,
    pub service_id: ServiceId,
    pub operation_id: String,
    pub timestamp: DateTime<Utc>,
    pub duration_ms: u64,
    pub metadata: HashMap<String, serde_json::Value>,
}

/// Service event for inter-service communication
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ServiceEvent {
    pub event_id: String,
    pub service_id: ServiceId,
    pub event_type: ServiceEventType,
    pub payload: serde_json::Value,
    pub timestamp: DateTime<Utc>,
    pub correlation_id: Option<String>,
}

/// Types of service events
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum ServiceEventType {
    /// Service lifecycle events
    ServiceStarted,
    ServiceStopped,
    ServicePaused,
    ServiceResumed,
    ServiceError,

    /// User-related events
    UserAuthenticated,
    UserLoggedOut,
    UserRegistered,

    /// Session events
    SessionCreated,
    SessionExpired,
    SessionUpdated,

    /// Cache events
    CacheHit,
    CacheMiss,
    CacheInvalidated,

    /// Health events
    HealthCheckPassed,
    HealthCheckFailed,

    /// Custom events
    Custom(String),
}

/// Service metrics for monitoring
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ServiceMetrics {
    pub service_id: ServiceId,
    pub uptime_seconds: u64,
    pub request_count: u64,
    pub error_count: u64,
    pub average_response_time_ms: f64,
    pub memory_usage_bytes: u64,
    pub cpu_usage_percent: f64,
    pub custom_metrics: HashMap<String, f64>,
    pub last_updated: DateTime<Utc>,
}

/// Service dependency information
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ServiceDependency {
    pub service_id: ServiceId,
    pub dependency_id: ServiceId,
    pub dependency_type: DependencyType,
    pub required: bool,
    pub health_status: HealthStatus,
}

/// Types of service dependencies
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum DependencyType {
    Database,
    Cache,
    MessageQueue,
    ExternalApi,
    InternalService,
    Configuration,
}

/// Service operation request base structure
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ServiceRequest<T> {
    pub request_id: String,
    pub service_id: ServiceId,
    pub user_id: Option<UserId>,
    pub session_id: Option<SessionId>,
    pub payload: T,
    pub timestamp: DateTime<Utc>,
    pub timeout_seconds: Option<u64>,
    pub metadata: HashMap<String, serde_json::Value>,
}

/// Service operation response base structure
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ServiceResponse<T> {
    pub request_id: String,
    pub service_id: ServiceId,
    pub success: bool,
    pub payload: Option<T>,
    pub error: Option<String>,
    pub timestamp: DateTime<Utc>,
    pub duration_ms: u64,
    pub metadata: HashMap<String, serde_json::Value>,
}

/// Service registry entry for service discovery
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ServiceRegistryEntry {
    pub service_id: ServiceId,
    pub service_type: String,
    pub version: String,
    pub status: ServiceStatus,
    pub health: HealthStatus,
    pub endpoints: Vec<String>,
    pub dependencies: Vec<ServiceDependency>,
    pub registered_at: DateTime<Utc>,
    pub last_heartbeat: DateTime<Utc>,
}

/// Service coordination message for inter-service communication
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ServiceMessage {
    pub message_id: String,
    pub from_service: ServiceId,
    pub to_service: ServiceId,
    pub message_type: ServiceMessageType,
    pub payload: serde_json::Value,
    pub timestamp: DateTime<Utc>,
    pub reply_to: Option<String>,
    pub correlation_id: Option<String>,
}

/// Types of service coordination messages
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum ServiceMessageType {
    Request,
    Response,
    Event,
    Heartbeat,
    Shutdown,
    Configuration,
}

/// Service performance statistics
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ServicePerformanceStats {
    pub service_id: ServiceId,
    pub requests_per_second: f64,
    pub average_latency_ms: f64,
    pub p95_latency_ms: f64,
    pub p99_latency_ms: f64,
    pub error_rate_percent: f64,
    pub throughput_bytes_per_second: f64,
    pub concurrent_connections: u32,
    pub measurement_window_seconds: u64,
    pub timestamp: DateTime<Utc>,
}