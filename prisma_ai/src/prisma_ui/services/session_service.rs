// =================================================================================================
// File: /prisma_ai/src/prisma_ui/services/session_service.rs
// =================================================================================================
// Purpose: Session management service for multi-device user state coordination and persistence.
// This service manages user sessions across multiple devices, handling login state, UI preferences,
// active conversations, project context, and real-time session synchronization. It provides
// seamless user experience across iPhone, Mac, and other devices in the family setup.
// =================================================================================================
// Internal Dependencies:
// - auth_service.rs: Authentication integration for session validation and user verification
// - cache_service.rs: Session data caching for performance optimization
// - types.rs: Session data types and user state structures
// - traits.rs: Session service trait definitions and abstractions
// - ../publishers/: Session event publishing for real-time multi-device synchronization
// =================================================================================================
// External Dependencies:
// - SurrealDB: Session persistence, user preferences, and multi-device state storage
// - RabbitMQ: Real-time session event publishing and device synchronization
// - chrono: Session timestamp management and expiration tracking
// - uuid: Unique identifier generation for sessions, devices, and state entities
// - tokio: Async session management and concurrent device coordination
// =================================================================================================
// Module Interactions:
// - Coordinates with authentication service for session validation and user context
// - Integrates with all UI services for session-aware operations and user context
// - Manages session data persistence and retrieval via SurrealDB storage
// - Publishes session events via RabbitMQ for real-time multi-device synchronization
// - Provides session context to chat, agent, project, and participant services
// =================================================================================================
// Session Management Features:
// - Multi-device Sessions: Device registration, session tracking, and state synchronization
// - User Preferences: UI settings, themes, and personalization across devices
// - Context Persistence: Active chats, current projects, and workflow state management
// - Real-time Sync: Live session updates and state changes across all user devices
// - Session Security: Session expiration, validation, and secure state management
// =================================================================================================
// Session Architecture:
// ┌─────────────────┐    Session Ops   ┌──────────────────────┐    Synchronization┌─────────────────┐
// │ Multi-device UI │ ──────────→      │ Session Management   │ ──────────→       │ Device          │
// │ - iPhone        │                  │ Service              │                   │ Coordination    │
// │ - Mac           │                  │ ├─Device Tracking    │                   │ - State Sync    │
// │ - Family Devices│                  │ ├─Preference Sync    │                   │ - Real-time     │
// └─────────────────┘                  │ ├─Context Management │                   │   Updates       │
//                                      │ └─Security Validation│                   │ - Conflict      │
//                                      └──────────────────────┘                   │   Resolution    │
//                                               ↓                                  └─────────────────┘
//                                      ┌──────────────────────┐                           ↓
//                                      │ Persistence Layer    │                   ┌─────────────────┐
//                                      │ - Session Storage    │ ←─────────────────│ Storage         │
//                                      │ - Preference Data    │                   │ - SurrealDB     │
//                                      │ - Context State      │                   │ - Session Data  │
//                                      └──────────────────────┘                   │ - Device Info   │
//                                                                                 └─────────────────┘
// =================================================================================================

use std::sync::Arc;
use std::collections::HashMap;
use tokio::sync::RwLock;
use serde::{Serialize, Deserialize};
use chrono::{DateTime, Utc, Duration};
use uuid::Uuid;
use tracing::{info, error, debug, warn};

// Import error handling
use crate::err::{PrismaResult, GenericError as PrismaError, DomainError};

// Import storage
use crate::storage::{Database, DataStore};

// Import auth service types
use crate::prisma_ui::services::auth_service::{User, Device};

/// Session data structure for database storage
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Session {
    pub id: String,
    pub user_id: String,
    pub device_id: String,
    pub is_active: bool,
    pub created_at: DateTime<Utc>,
    pub last_activity: DateTime<Utc>,
    pub expires_at: DateTime<Utc>,
    pub preferences: UserPreferences,
    pub context: SessionContext,
}

/// User preferences for personalization
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct UserPreferences {
    pub theme: String,
    pub language: String,
    pub notifications_enabled: bool,
    pub auto_save_enabled: bool,
    pub ui_density: UIDensity,
    pub custom_settings: HashMap<String, serde_json::Value>,
}

/// UI density settings
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum UIDensity {
    Compact,
    Normal,
    Comfortable,
}

/// Session context for active state management
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SessionContext {
    pub active_chat_id: Option<String>,
    pub active_project_id: Option<String>,
    pub active_agent_id: Option<String>,
    pub open_tabs: Vec<TabInfo>,
    pub workflow_state: Option<WorkflowState>,
    pub last_location: String,
}

/// Tab information for multi-tab support
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct TabInfo {
    pub id: String,
    pub tab_type: TabType,
    pub title: String,
    pub url: String,
    pub is_active: bool,
    pub created_at: DateTime<Utc>,
}

/// Types of tabs in the UI
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum TabType {
    Chat,
    Project,
    Agent,
    Settings,
    Dashboard,
}

/// Workflow state for complex operations
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct WorkflowState {
    pub workflow_id: String,
    pub workflow_type: String,
    pub current_step: u32,
    pub total_steps: u32,
    pub state_data: HashMap<String, serde_json::Value>,
    pub created_at: DateTime<Utc>,
    pub updated_at: DateTime<Utc>,
}

/// Session creation request
#[derive(Debug, Deserialize)]
pub struct CreateSessionRequest {
    pub user_id: String,
    pub device_id: String,
    pub preferences: Option<UserPreferences>,
}

/// Session update request
#[derive(Debug, Deserialize)]
pub struct UpdateSessionRequest {
    pub preferences: Option<UserPreferences>,
    pub context: Option<SessionContext>,
}

/// Session synchronization event
#[derive(Debug, Serialize)]
pub struct SessionSyncEvent {
    pub session_id: String,
    pub user_id: String,
    pub event_type: SyncEventType,
    pub data: serde_json::Value,
    pub timestamp: DateTime<Utc>,
}

/// Types of synchronization events
#[derive(Debug, Serialize)]
pub enum SyncEventType {
    PreferencesUpdated,
    ContextChanged,
    TabOpened,
    TabClosed,
    WorkflowStarted,
    WorkflowCompleted,
}

/// Session management service for multi-device coordination
pub struct SessionService {
    database: Arc<Database>,
    active_sessions: Arc<RwLock<HashMap<String, Session>>>,
    session_timeout_hours: i64,
}

impl SessionService {
    /// Create a new SessionService instance
    pub async fn new(database: Arc<Database>, session_timeout_hours: i64) -> PrismaResult<Self> {
        Ok(Self {
            database,
            active_sessions: Arc::new(RwLock::new(HashMap::new())),
            session_timeout_hours,
        })
    }

    /// Create a new session for a user and device
    pub async fn create_session(&self, request: CreateSessionRequest) -> PrismaResult<Session> {
        info!("Creating session for user: {} on device: {}", request.user_id, request.device_id);

        // Check if user exists (this would typically be done via auth service)
        // For now, we'll assume the user_id is valid

        // Create default preferences if not provided
        let preferences = request.preferences.unwrap_or_else(|| UserPreferences {
            theme: "dark".to_string(),
            language: "en".to_string(),
            notifications_enabled: true,
            auto_save_enabled: true,
            ui_density: UIDensity::Normal,
            custom_settings: HashMap::new(),
        });

        // Create default context
        let context = SessionContext {
            active_chat_id: None,
            active_project_id: None,
            active_agent_id: None,
            open_tabs: Vec::new(),
            workflow_state: None,
            last_location: "/dashboard".to_string(),
        };

        let session_id = Uuid::new_v4().to_string();
        let now = Utc::now();
        let expires_at = now + Duration::hours(self.session_timeout_hours);

        let session = Session {
            id: session_id.clone(),
            user_id: request.user_id.clone(),
            device_id: request.device_id.clone(),
            is_active: true,
            created_at: now,
            last_activity: now,
            expires_at,
            preferences,
            context,
        };

        // Store session in database
        self.store_session(&session).await?;

        // Add to active sessions cache
        {
            let mut active_sessions = self.active_sessions.write().await;
            active_sessions.insert(session_id.clone(), session.clone());
        }

        info!("Successfully created session: {}", session_id);
        Ok(session)
    }

    /// Get session by ID
    pub async fn get_session(&self, session_id: &str) -> PrismaResult<Option<Session>> {
        // First check active sessions cache
        {
            let active_sessions = self.active_sessions.read().await;
            if let Some(session) = active_sessions.get(session_id) {
                if session.is_active && session.expires_at > Utc::now() {
                    return Ok(Some(session.clone()));
                }
            }
        }

        // If not in cache or expired, check database
        self.get_session_from_db(session_id).await
    }

    /// Update session activity timestamp
    pub async fn update_activity(&self, session_id: &str) -> PrismaResult<()> {
        debug!("Updating activity for session: {}", session_id);

        // Update in cache
        {
            let mut active_sessions = self.active_sessions.write().await;
            if let Some(session) = active_sessions.get_mut(session_id) {
                session.last_activity = Utc::now();
            }
        }

        // Update in database
        let sessions: Vec<Session> = self.database.client()
            .select("session")
            .await
            .map_err(|e| PrismaError::from(DomainError::DatabaseError(
                format!("Failed to query sessions: {}", e)
            )))?;

        if let Some(mut session) = sessions.into_iter().find(|s| s.id == session_id) {
            session.last_activity = Utc::now();
            self.store_session(&session).await?;
        }

        Ok(())
    }

    /// Update session preferences
    pub async fn update_preferences(&self, session_id: &str, preferences: UserPreferences) -> PrismaResult<()> {
        info!("Updating preferences for session: {}", session_id);

        // Update in cache
        {
            let mut active_sessions = self.active_sessions.write().await;
            if let Some(session) = active_sessions.get_mut(session_id) {
                session.preferences = preferences.clone();
                session.last_activity = Utc::now();
            }
        }

        // Update in database
        let sessions: Vec<Session> = self.database.client()
            .select("session")
            .await
            .map_err(|e| PrismaError::from(DomainError::DatabaseError(
                format!("Failed to query sessions: {}", e)
            )))?;

        if let Some(mut session) = sessions.into_iter().find(|s| s.id == session_id) {
            session.preferences = preferences;
            session.last_activity = Utc::now();
            self.store_session(&session).await?;
        }

        Ok(())
    }

    /// Update session context
    pub async fn update_context(&self, session_id: &str, context: SessionContext) -> PrismaResult<()> {
        debug!("Updating context for session: {}", session_id);

        // Update in cache
        {
            let mut active_sessions = self.active_sessions.write().await;
            if let Some(session) = active_sessions.get_mut(session_id) {
                session.context = context.clone();
                session.last_activity = Utc::now();
            }
        }

        // Update in database
        let sessions: Vec<Session> = self.database.client()
            .select("session")
            .await
            .map_err(|e| PrismaError::from(DomainError::DatabaseError(
                format!("Failed to query sessions: {}", e)
            )))?;

        if let Some(mut session) = sessions.into_iter().find(|s| s.id == session_id) {
            session.context = context;
            session.last_activity = Utc::now();
            self.store_session(&session).await?;
        }

        Ok(())
    }

    /// Get all active sessions for a user
    pub async fn get_user_sessions(&self, user_id: &str) -> PrismaResult<Vec<Session>> {
        let sessions: Vec<Session> = self.database.client()
            .select("session")
            .await
            .map_err(|e| PrismaError::from(DomainError::DatabaseError(
                format!("Failed to query sessions: {}", e)
            )))?;

        let user_sessions: Vec<Session> = sessions.into_iter()
            .filter(|s| s.user_id == user_id && s.is_active && s.expires_at > Utc::now())
            .collect();

        Ok(user_sessions)
    }

    /// Terminate session
    pub async fn terminate_session(&self, session_id: &str) -> PrismaResult<()> {
        info!("Terminating session: {}", session_id);

        // Remove from cache
        {
            let mut active_sessions = self.active_sessions.write().await;
            active_sessions.remove(session_id);
        }

        // Update in database
        let sessions: Vec<Session> = self.database.client()
            .select("session")
            .await
            .map_err(|e| PrismaError::from(DomainError::DatabaseError(
                format!("Failed to query sessions: {}", e)
            )))?;

        if let Some(mut session) = sessions.into_iter().find(|s| s.id == session_id) {
            session.is_active = false;
            self.store_session(&session).await?;
        }

        Ok(())
    }

    /// Terminate all sessions for a user
    pub async fn terminate_user_sessions(&self, user_id: &str) -> PrismaResult<()> {
        info!("Terminating all sessions for user: {}", user_id);

        let sessions = self.get_user_sessions(user_id).await?;

        for session in sessions {
            self.terminate_session(&session.id).await?;
        }

        Ok(())
    }

    /// Clean up expired sessions
    pub async fn cleanup_expired_sessions(&self) -> PrismaResult<()> {
        debug!("Cleaning up expired sessions");

        let now = Utc::now();
        let sessions: Vec<Session> = self.database.client()
            .select("session")
            .await
            .map_err(|e| PrismaError::from(DomainError::DatabaseError(
                format!("Failed to query sessions: {}", e)
            )))?;

        let expired_sessions: Vec<Session> = sessions.into_iter()
            .filter(|s| s.expires_at <= now || !s.is_active)
            .collect();

        for session in expired_sessions {
            // Remove from cache
            {
                let mut active_sessions = self.active_sessions.write().await;
                active_sessions.remove(&session.id);
            }

            // Mark as inactive in database
            let mut inactive_session = session;
            inactive_session.is_active = false;
            self.store_session(&inactive_session).await?;
        }

        info!("Cleaned up expired sessions");
        Ok(())
    }

    /// Synchronize session across devices
    pub async fn sync_session(&self, session_id: &str, target_device_ids: Vec<String>) -> PrismaResult<()> {
        debug!("Synchronizing session {} to devices: {:?}", session_id, target_device_ids);

        let session = self.get_session(session_id).await?
            .ok_or_else(|| PrismaError::from(DomainError::ValidationError(
                "Session not found".to_string()
            )))?;

        // Create sync event
        let sync_event = SessionSyncEvent {
            session_id: session_id.to_string(),
            user_id: session.user_id.clone(),
            event_type: SyncEventType::PreferencesUpdated,
            data: serde_json::to_value(&session.preferences)
                .map_err(|e| PrismaError::from(DomainError::SerializationError(
                    format!("Failed to serialize preferences: {}", e)
                )))?,
            timestamp: Utc::now(),
        };

        // TODO: Publish sync event to RabbitMQ for real-time synchronization
        // This would be implemented when the publisher system is ready

        info!("Session synchronized successfully");
        Ok(())
    }

    /// Store session in database
    async fn store_session(&self, session: &Session) -> PrismaResult<()> {
        let session_value = serde_json::to_value(session)
            .map_err(|e| PrismaError::from(DomainError::SerializationError(
                format!("Failed to serialize session: {}", e)
            )))?;

        let _stored: Option<Session> = self.database.client()
            .create(("session", &session.id))
            .content(session_value)
            .await
            .map_err(|e| PrismaError::from(DomainError::DatabaseError(
                format!("Failed to store session: {}", e)
            )))?;

        Ok(())
    }

    /// Get session from database
    async fn get_session_from_db(&self, session_id: &str) -> PrismaResult<Option<Session>> {
        let sessions: Vec<Session> = self.database.client()
            .select("session")
            .await
            .map_err(|e| PrismaError::from(DomainError::DatabaseError(
                format!("Failed to query sessions: {}", e)
            )))?;

        Ok(sessions.into_iter().find(|s| s.id == session_id))
    }

    /// Add tab to session context
    pub async fn add_tab(&self, session_id: &str, tab: TabInfo) -> PrismaResult<()> {
        debug!("Adding tab to session: {}", session_id);

        let session = self.get_session(session_id).await?
            .ok_or_else(|| PrismaError::from(DomainError::ValidationError(
                "Session not found".to_string()
            )))?;

        let mut updated_context = session.context.clone();
        updated_context.open_tabs.push(tab);

        self.update_context(session_id, updated_context).await
    }

    /// Remove tab from session context
    pub async fn remove_tab(&self, session_id: &str, tab_id: &str) -> PrismaResult<()> {
        debug!("Removing tab from session: {}", session_id);

        let session = self.get_session(session_id).await?
            .ok_or_else(|| PrismaError::from(DomainError::ValidationError(
                "Session not found".to_string()
            )))?;

        let mut updated_context = session.context.clone();
        updated_context.open_tabs.retain(|tab| tab.id != tab_id);

        self.update_context(session_id, updated_context).await
    }

    /// Update workflow state
    pub async fn update_workflow_state(&self, session_id: &str, workflow_state: WorkflowState) -> PrismaResult<()> {
        debug!("Updating workflow state for session: {}", session_id);

        let session = self.get_session(session_id).await?
            .ok_or_else(|| PrismaError::from(DomainError::ValidationError(
                "Session not found".to_string()
            )))?;

        let mut updated_context = session.context.clone();
        updated_context.workflow_state = Some(workflow_state);

        self.update_context(session_id, updated_context).await
    }

    /// Get session statistics for monitoring
    pub async fn get_session_stats(&self) -> PrismaResult<SessionStats> {
        let sessions: Vec<Session> = self.database.client()
            .select("session")
            .await
            .map_err(|e| PrismaError::from(DomainError::DatabaseError(
                format!("Failed to query sessions: {}", e)
            )))?;

        let now = Utc::now();
        let active_sessions = sessions.iter().filter(|s| s.is_active && s.expires_at > now).count();
        let total_sessions = sessions.len();
        let expired_sessions = sessions.iter().filter(|s| s.expires_at <= now).count();

        Ok(SessionStats {
            total_sessions,
            active_sessions,
            expired_sessions,
            cache_size: self.active_sessions.read().await.len(),
        })
    }
}

/// Session statistics for monitoring
#[derive(Debug, Serialize)]
pub struct SessionStats {
    pub total_sessions: usize,
    pub active_sessions: usize,
    pub expired_sessions: usize,
    pub cache_size: usize,
}
