// =================================================================================================
// File: /prisma_ai/src/prisma_ui/mod.rs
// =================================================================================================
// Purpose: Main module declaration for the Prisma UI integration layer.
// This module serves as the central hub for UI-related functionality, bridging the gap between
// the Flutter frontend and the Rust backend. It coordinates services, publishers, and data flow
// to enable seamless communication between the user interface and the core PRISMA system.
// =================================================================================================
// Internal Dependencies:
// - services/: UI service layer for business logic and request handling
// - publishers/: Event publishing system for UI communication via RabbitMQ
// - types.rs: Core data types and structures for UI operations
// - traits.rs: Trait definitions for UI component interfaces
// - generics.rs: Generic implementations and utilities
// - prisma_ui.rs: Main UI coordination and management logic
// =================================================================================================
// External Dependencies:
// - RabbitMQ: Message broker for frontend-backend communication
// - Flutter UI: Frontend application consuming published events
// - PrismaEngine: Core engine for task execution and agent management
// - SurrealDB: Database for UI state and message persistence
// =================================================================================================
// Module Interactions:
// - Interfaces with PrismaEngine for task submission and execution
// - Communicates with Agent Manager for agent creation and configuration
// - Publishes events to RabbitMQ for Flutter UI consumption
// - Handles chat services and real-time streaming functionality
// - Manages project creation and participant configuration workflows
// - Integrates with pm.toml configuration for dynamic UI behavior
// =================================================================================================
// Architecture Overview:
// ┌─────────────────┐    RabbitMQ    ┌──────────────────────┐
// │ Flutter UI      │ ←──────────→   │ prisma_ui (Rust)     │
// │ /prisma_ui/lib/ │                │ ├─services/          │
// │                 │                │ │ ├─chat_service     │
// │                 │                │ │ ├─agent_service    │
// │                 │                │ │ └─mcp_service      │
// │                 │                │ └─publishers/        │
// │                 │                │   ├─chat_publisher   │
// │                 │                │   ├─rabbitmq         │
// │                 │                │   └─streaming        │
// └─────────────────┘                └──────────────────────┘
// =================================================================================================

// Declare submodules within prisma_ui
pub mod services;
pub mod middleware; // Middleware module for request/response processing
pub mod types; // Core data types and structures for UI operations
pub mod traits; // Trait definitions for UI component interfaces
pub mod generics; // Generic implementations and utilities
pub mod prisma_ui; // Main UI coordination and management logic
pub mod publishers; // Publishers module for sending data to the UI



// Re-export items if needed for easier access from outside prisma_ui
// pub use services::BasicAgentService; // Example re-export
// pub use middleware::MiddlewareChain; // Example middleware re-export (commented until implementation)