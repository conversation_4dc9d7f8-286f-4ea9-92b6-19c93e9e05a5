use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use std::fs;
use std::path::{Path, PathBuf};
use std::sync::{Arc, RwLock};
use std::time::SystemTime;
use tracing::Level;

// Import the new types from prisma/config
use crate::prisma::config::types::{TelemetryConfig, EnvironmentSettings, Settings, DatabaseConfig, RedisConfig, GlobalConfig, RabbitMQConfig, GlobalEndpoints, SurrealDBConfig, LLMConfig, SystemErrorConfig, TelemetryRabbitMQConfig};

// NOTE: All configuration structs have been moved to prisma_ai/src/prisma/config/types.rs
// They are re-exported in telemetry/core/mod.rs for backward compatibility
// This file now only contains the DynamicConfig functionality

// Add a wrapper struct to handle reloading
pub struct DynamicConfig {
    config: Arc<RwLock<TelemetryConfig>>,
    config_path: PathBuf,
    last_modified: SystemTime,
}

impl DynamicConfig {
    /// Create a new DynamicConfig instance
    pub fn new(config_path: PathBuf) -> Result<Self, Box<dyn std::error::Error>> {
        let config = Self::load_config(&config_path)?;
        let last_modified = fs::metadata(&config_path)?.modified()?;
        
        Ok(Self {
            config: Arc::new(RwLock::new(config)),
            config_path,
            last_modified,
        })
    }

    /// Load configuration from file
    fn load_config(path: &Path) -> Result<TelemetryConfig, Box<dyn std::error::Error>> {
        let contents = fs::read_to_string(path)?;
        let settings: Settings = toml::from_str(&contents)?;
        Ok(settings.telemetry)
    }

    /// Get a copy of the current configuration
    pub fn get_config(&self) -> TelemetryConfig {
        self.config.read().unwrap().clone()
    }

    /// Check if the configuration file has been modified and reload if necessary
    pub fn reload_if_changed(&mut self) -> Result<bool, Box<dyn std::error::Error>> {
        let metadata = fs::metadata(&self.config_path)?;
        let modified = metadata.modified()?;
        
        if modified > self.last_modified {
            let new_config = Self::load_config(&self.config_path)?;
            *self.config.write().unwrap() = new_config;
            self.last_modified = modified;
            Ok(true)
        } else {
            Ok(false)
        }
    }

    /// Force reload the configuration
    pub fn reload(&mut self) -> Result<(), Box<dyn std::error::Error>> {
        let new_config = Self::load_config(&self.config_path)?;
        *self.config.write().unwrap() = new_config;
        self.last_modified = fs::metadata(&self.config_path)?.modified()?;
        Ok(())
    }
}

impl TelemetryConfig {
    /// Create a new TelemetryConfig with custom settings
    pub fn new(
        prometheus_enabled: bool,
        prometheus_endpoint: String,
        loki_enabled: bool,
        loki_endpoint: String,
        grafana_enabled: bool,
        grafana_endpoint: String,
        env_settings: EnvironmentSettings,
        grafana_service_token: String,
        rabbitmq: Option<TelemetryRabbitMQConfig>,
        surrealdb: SurrealDBConfig,
    ) -> Self {
        Self {
            endpoints: GlobalEndpoints {
                prometheus_enabled,
                prometheus: prometheus_endpoint,
                loki_enabled,
                loki: loki_endpoint,
                loki_version: Some("3.3.0".to_string()),
                grafana_enabled,
                grafana: grafana_endpoint,
                grafana_version: Some("11.4.0".to_string()),
            },
            env_settings,
            grafana_service_token,
            rabbitmq,
            surrealdb,
            llm: LLMConfig::default(),
            system_error: SystemErrorConfig::default(),
        }
    }

    /// Load configuration from a TOML file
    pub fn load_from_file(path: impl AsRef<Path>) -> Result<Self, Box<dyn std::error::Error>> {
        let contents = fs::read_to_string(path)?;
        let settings: Settings = toml::from_str(&contents)?;
        Ok(settings.telemetry)
    }

    /// Save configuration to a TOML file
    pub fn save_to_file(&self, path: impl AsRef<Path>) -> Result<(), Box<dyn std::error::Error>> {
        // Create a Settings struct with this telemetry config
        let settings = Settings {
            global: GlobalConfig::default(),
            database: DatabaseConfig::default(),
            redis: RedisConfig::default(),
            rabbitmq: RabbitMQConfig::default(),
            telemetry: self.clone(),
        };
        
        let contents = toml::to_string_pretty(&settings)?;
        fs::write(path, contents)?;
        Ok(())
    }

    /// Get the Prometheus endpoint if enabled
    pub fn prometheus_endpoint(&self) -> Option<&str> {
        if self.endpoints.prometheus_enabled {
            Some(&self.endpoints.prometheus)
        } else {
            None
        }
    }

    /// Get the Loki endpoint if enabled
    pub fn loki_endpoint(&self) -> Option<&str> {
        if self.endpoints.loki_enabled {
            Some(&self.endpoints.loki)
        } else {
            None
        }
    }

    /// Get the Grafana endpoint if enabled
    pub fn grafana_endpoint(&self) -> Option<&str> {
        if self.endpoints.grafana_enabled {
            Some(&self.endpoints.grafana)
        } else {
            None
        }
    }

    /// Check if RabbitMQ telemetry is enabled
    pub fn is_rabbitmq_enabled(&self) -> bool {
        self.rabbitmq.is_some()
    }

    /// Get RabbitMQ configuration if available
    pub fn rabbitmq_config(&self) -> Option<&TelemetryRabbitMQConfig> {
        self.rabbitmq.as_ref()
    }
}
