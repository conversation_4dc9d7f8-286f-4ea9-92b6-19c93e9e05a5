use crate::chat::types::{ChatSessionMetadata, MetadataTrackingConfig};
use chrono::{DateTime, Utc};
use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use std::path::{Path, PathBuf};
use tracing::Level;

// Serde module for tracing::Level serialization
mod level_serde {
    use serde::{Deserialize, Deserializer, Serialize, Serializer};
    use tracing::Level;

    pub fn serialize<S>(level: &Level, serializer: S) -> Result<S::Ok, S::Error>
    where
        S: Serializer,
    {
        level.to_string().serialize(serializer)
    }

    pub fn deserialize<'de, D>(deserializer: D) -> Result<Level, D::Error>
    where
        D: Deserializer<'de>,
    {
        let s = String::deserialize(deserializer)?;
        match s.to_uppercase().as_str() {
            "TRACE" => Ok(Level::TRACE),
            "DEBUG" => Ok(Level::DEBUG),
            "INFO" => Ok(Level::INFO),
            "WARN" => Ok(Level::WARN),
            "ERROR" => Ok(Level::ERROR),
            _ => Err(serde::de::Error::custom(format!("Invalid log level: {}", s))),
        }
    }
}

/// Main configuration structure loaded from the TOML file
#[derive(Debug, Clone, Serialize, Deserialize, Default)]
pub struct ProjectConfig {
    #[serde(default)]
    pub prisma_engine_initialization: PrismaEngineConfig,
    #[serde(default)]
    pub project_metadata: ProjectMetadata,
    #[serde(default)]
    pub session_tracking: MetadataTrackingConfig,
    #[serde(default)]
    pub new_project: NewProjectConfig,
}

#[derive(Debug, Clone, Serialize, Deserialize, Default)]
pub struct PrismaEngineConfig {
    pub enable_prisma_engine: bool,
}

/// Project metadata configuration
#[derive(Debug, Clone, Serialize, Deserialize, Default)]
pub struct ProjectMetadata {
    pub version: String,
    pub export_format: String,
}

/// Represents a complete project with all its data
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Project {
    pub id: String,
    pub name: String,
    pub description: String,
    pub created_at: DateTime<Utc>,
    pub updated_at: DateTime<Utc>,
    pub config: ProjectConfig,
    pub sessions: HashMap<String, ChatSessionMetadata>,
    pub file_path: Option<PathBuf>,
}

/// Project status enum
#[derive(Debug, Clone, Copy, PartialEq, Eq, Serialize, Deserialize)]
pub enum ProjectStatus {
    Active,
    Archived,
    Deleted,
}

/// Project creation parameters
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ProjectParams {
    pub name: String,
    pub description: String,
}

/// Project summary for display in UI
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ProjectSummary {
    pub id: String,
    pub name: String,
    pub description: String,
    pub created_at: DateTime<Utc>,
    pub updated_at: DateTime<Utc>,
    pub session_count: usize,
    pub total_messages: u32,
    pub status: ProjectStatus,
}

/// Response type for project operations
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ProjectResponse {
    pub success: bool,
    pub message: String,
    pub project: Option<Project>,
}

/// Form field configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct FormField {
    pub name: String,
    pub field_type: String,
    pub required: bool,
    pub label: String,
    pub multiline: Option<bool>,
}

/// New project configuration settings
#[derive(Debug, Clone, Serialize, Deserialize, Default)]
pub struct NewProjectConfig {
    #[serde(default)]
    pub fields: Vec<FormField>,
    #[serde(default)]
    pub embedding_model: EmbeddingModelConfig,
}

/// Embedding model configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct EmbeddingModelConfig {
    pub required: bool,
    pub default_model: String,
    pub model_list_source: String,
}

impl Default for EmbeddingModelConfig {
    fn default() -> Self {
        Self {
            required: true,
            default_model: "nomic-embed-text-v2-moe.f32.gguf".to_string(),
            model_list_source: "llama_models.embedding_only".to_string(),
        }
    }
}

// ============================================================================
// Settings Configuration (moved from telemetry/core/config.rs)
// ============================================================================

/// Main settings configuration structure loaded from settings.toml
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Settings {
    pub global: GlobalConfig,
    pub database: DatabaseConfig,
    pub redis: RedisConfig,
    pub rabbitmq: RabbitMQConfig,
    pub telemetry: TelemetryConfig,
}

/// Database connection configuration (migrated from config.yaml)
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct DatabaseConfig {
    pub host: String,
    pub port: u16,
    pub username: String,
    pub password: String,
    pub namespace: String,
    pub database: String,
    pub use_http: bool,
    pub version: String,
}

/// Redis cache configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct RedisConfig {
    pub host: String,
    pub port: u16,
    pub password: String,
    pub database: u8,
    pub connection_timeout_ms: u64,
    pub command_timeout_ms: u64,
    pub max_connections: u32,
    pub version: String,
    pub cache: RedisCacheConfig,
    pub performance: RedisPerformanceConfig,
}

/// Redis cache-specific configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct RedisCacheConfig {
    pub default_ttl_seconds: u64,
    pub max_memory_policy: String,
    pub max_memory: String,
}

/// Redis performance configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct RedisPerformanceConfig {
    pub enable_pipelining: bool,
    pub pipeline_batch_size: u32,
    pub enable_compression: bool,
    pub compression_threshold_bytes: u32,
}

/// Global configuration settings
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct GlobalConfig {
    pub service_name: String,
    pub environment: String,
    pub service_id: String,
    pub endpoints: GlobalEndpoints,
    pub api_keys: GlobalApiKeys,
    pub alerts: GlobalAlerts,
}

/// Global endpoints configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct GlobalEndpoints {
    pub prometheus_enabled: bool,
    pub prometheus: String,
    pub grafana_enabled: bool,
    pub grafana: String,
    pub grafana_version: Option<String>,
    pub loki_enabled: bool,
    pub loki: String,
    pub loki_version: Option<String>,
}

/// Global API keys configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct GlobalApiKeys {
    pub prisma_ai_enabled: bool,
    pub slack_enabled: bool,
    pub slack_token: String,
    pub slack_channel: String,
    pub discord_enabled: bool,
    pub discord_webhook: String,
    pub gmail_enabled: bool,
    pub gmail_email: String,
    pub gmail_app_password: String,
}

/// Global alerts configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct GlobalAlerts {
    pub enabled: bool,
    pub default_log_level: String,
    pub enabled_modules: Vec<String>,
    pub slack: AlertSlackConfig,
    pub discord: AlertDiscordConfig,
    pub email: AlertEmailConfig,
}

/// Slack alert configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AlertSlackConfig {
    pub enabled: bool,
    pub log_levels: Vec<String>,
    pub default_level: String,
    pub modules: Vec<String>,
    pub message_template: String,
}

/// Discord alert configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AlertDiscordConfig {
    pub enabled: bool,
    pub log_levels: Vec<String>,
    pub default_level: String,
    pub modules: Vec<String>,
    pub message_template: String,
}

/// Email alert configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AlertEmailConfig {
    pub enabled: bool,
    pub log_levels: Vec<String>,
    pub default_level: String,
    pub modules: Vec<String>,
    pub subject_template: String,
    pub body_template: String,
}

/// Environment settings
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct EnvironmentSettings {
    pub service_name: String,
    pub environment: String,
    pub service_id: String,
}

/// RabbitMQ configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct RabbitMQConfig {
    pub host: String,
    pub port: u16,
    pub username: String,
    pub password: String,
    pub vhost: String,
    pub connection_name: String,
    pub metrics: RabbitMQMetricsConfig,
    pub telemetry: RabbitMQTelemetryConfig,
    pub alerts: RabbitMQAlertsConfig,
    pub dashboards: RabbitMQDashboardsConfig,
}

/// RabbitMQ metrics configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct RabbitMQMetricsConfig {
    pub connection_metrics_enabled: bool,
    pub connection_latency_buckets: Vec<f64>,
    pub tls_metrics_enabled: bool,
    pub channel_metrics_enabled: bool,
    pub channel_latency_buckets: Vec<f64>,
    pub message_metrics_enabled: bool,
    pub message_size_buckets: Vec<f64>,
    pub message_processing_buckets: Vec<f64>,
    pub queue_metrics_enabled: bool,
    pub queue_size_enabled: bool,
    pub queue_memory_enabled: bool,
    pub queue_consumer_enabled: bool,
    pub queue_growth_rate_enabled: bool,
    pub exchange_metrics_enabled: bool,
    pub exchange_binding_enabled: bool,
    pub routing_metrics_enabled: bool,
    pub consumer_metrics_enabled: bool,
    pub consumer_latency_buckets: Vec<f64>,
    pub consumer_utilization_enabled: bool,
    pub consumer_lag_enabled: bool,
}

/// RabbitMQ telemetry configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct RabbitMQTelemetryConfig {
    pub connection_name: String,
    pub enable_connection_tracing: bool,
    pub enable_channel_tracing: bool,
    pub enable_queue_tracing: bool,
    pub enable_exchange_tracing: bool,
    pub enable_consumer_tracing: bool,
    pub enable_publisher_tracing: bool,
    pub sampling_rate: f64,
    pub metrics: RabbitMQMetricsConfig,
    pub logging: RabbitMQLoggingConfig,
}

/// RabbitMQ alerts configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct RabbitMQAlertsConfig {
    pub enabled: bool,
    pub log_levels: Vec<String>,
    pub alert_on_connection_issues: bool,
    pub alert_on_channel_issues: bool,
    pub alert_on_queue_issues: bool,
    pub alert_on_consumer_issues: bool,
    pub alert_on_publisher_issues: bool,
    pub thresholds: RabbitMQAlertThresholds,
}

/// RabbitMQ alert thresholds
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct RabbitMQAlertThresholds {
    pub connection_timeout_ms: u64,
    pub queue_full_percentage: u8,
    pub message_rate_threshold: u64,
    pub consumer_lag_threshold: u64,
}

/// RabbitMQ dashboards configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct RabbitMQDashboardsConfig {
    pub overview_refresh: String,
    pub overview_tags: Vec<String>,
    pub connection_refresh: String,
    pub connection_tags: Vec<String>,
    pub performance_refresh: String,
    pub performance_tags: Vec<String>,
    pub queue_exchange_refresh: String,
    pub queue_exchange_tags: Vec<String>,
    pub error_refresh: String,
    pub error_tags: Vec<String>,
}

/// RabbitMQ logging configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct RabbitMQLoggingConfig {
    #[serde(with = "level_serde")]
    pub min_level: Level,
    pub stream_name: String,
    pub components: RabbitMQLoggingComponents,
    pub labels: RabbitMQLoggingLabels,
    pub performance: RabbitMQPerformanceThresholds,
}

/// RabbitMQ logging components
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct RabbitMQLoggingComponents {
    pub connection: bool,
    pub channel: bool,
    pub queue: bool,
    pub exchange: bool,
    pub publish: bool,
    pub consume: bool,
    pub routing: bool,
    pub performance: bool,
}

/// RabbitMQ logging labels
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct RabbitMQLoggingLabels {
    pub service: String,
    pub environment: String,
}

/// RabbitMQ performance thresholds
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct RabbitMQPerformanceThresholds {
    pub connection_warning_threshold_ms: u64,
    pub channel_warning_threshold_ms: u64,
    pub queue_warning_threshold_ms: u64,
    pub publish_warning_threshold_ms: u64,
    pub consume_warning_threshold_ms: u64,
}

/// Telemetry configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct TelemetryConfig {
    pub endpoints: GlobalEndpoints,
    pub env_settings: EnvironmentSettings,
    pub grafana_service_token: String,
    #[serde(skip_serializing_if = "Option::is_none")]
    pub rabbitmq: Option<TelemetryRabbitMQConfig>,
    pub surrealdb: SurrealDBConfig,
    pub llm: LLMConfig,
    pub system_error: SystemErrorConfig,
}

/// Telemetry-specific RabbitMQ configuration (for telemetry/monitoring purposes only)
/// Note: This is separate from the YAML config RabbitMQ which is for authentication
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct TelemetryRabbitMQConfig {
    // Connection details for telemetry RabbitMQ (separate from auth RabbitMQ)
    pub host: String,
    pub port: u16,
    pub username: String,
    pub password: String,
    pub vhost: String,
    pub connection_name: String,

    // Monitoring settings
    pub enable_connection_metrics: bool,
    pub enable_channel_metrics: bool,
    pub enable_message_metrics: bool,
    pub sampling_rate: f64,

    // Nested metrics configuration
    pub metrics: RabbitMQMetricsConfig,
}

/// SurrealDB telemetry configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SurrealDBConfig {
    pub connection_name: String,
    pub enable_query_tracing: bool,
    pub enable_transaction_tracing: bool,
    pub enable_document_tracing: bool,
    pub enable_index_tracing: bool,
    pub sampling_rate: f64,
    pub logging: SurrealDBLoggingConfig,
    pub metrics: SurrealDBMetricsConfig,
    pub alerts: SurrealDBAlertsConfig,
}

/// SurrealDB logging configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SurrealDBLoggingConfig {
    #[serde(with = "level_serde")]
    pub min_level: Level,
    pub stream_name: String,
    pub components: SurrealDBLoggingComponents,
    pub labels: SurrealDBLoggingLabels,
    pub query: SurrealDBQueryLogging,
    pub transaction: SurrealDBTransactionLogging,
    pub document: SurrealDBDocumentLogging,
    pub index: SurrealDBIndexLogging,
    pub rag: SurrealDBRagLogging,
    pub performance: SurrealDBPerformanceThresholds,
    pub loki: SurrealDBLokiConfig,
}

/// SurrealDB logging components
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SurrealDBLoggingComponents {
    pub query: bool,
    pub transaction: bool,
    pub document: bool,
    pub index: bool,
    pub authentication: bool,
    pub performance: bool,
    pub rag: bool,
}

/// SurrealDB logging labels
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SurrealDBLoggingLabels {
    pub service: String,
    pub environment: String,
}

/// SurrealDB query logging configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SurrealDBQueryLogging {
    pub log_slow_queries: bool,
    pub slow_query_threshold_ms: u64,
    pub log_query_plans: bool,
    pub log_query_cache_events: bool,
}

/// SurrealDB transaction logging configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SurrealDBTransactionLogging {
    pub log_begin_commit: bool,
    pub log_rollbacks: bool,
    pub log_deadlocks: bool,
    pub log_lock_wait_time: bool,
}

/// SurrealDB document logging configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SurrealDBDocumentLogging {
    pub log_mutations: bool,
    pub log_batch_operations: bool,
    pub log_validation_errors: bool,
}

/// SurrealDB index logging configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SurrealDBIndexLogging {
    pub log_builds: bool,
    pub log_updates: bool,
    pub log_analysis: bool,
}

/// SurrealDB RAG logging configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SurrealDBRagLogging {
    pub log_embeddings: bool,
    pub log_vector_searches: bool,
    pub log_document_processing: bool,
}

/// SurrealDB performance thresholds
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SurrealDBPerformanceThresholds {
    pub query_warning_threshold_ms: u64,
    pub transaction_warning_threshold_ms: u64,
    pub index_warning_threshold_ms: u64,
    pub embedding_warning_threshold_ms: u64,
    pub vector_search_warning_threshold_ms: u64,
    pub document_processing_warning_threshold_ms: u64,
}

/// SurrealDB Loki configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SurrealDBLokiConfig {
    pub endpoint: String,
    pub batch_size: u64,
    pub batch_wait_ms: u64,
    pub retention_days: u64,
    pub labels: SurrealDBLokiLabels,
}

/// SurrealDB Loki labels
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SurrealDBLokiLabels {
    pub app: String,
    pub component: String,
}

/// SurrealDB metrics configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SurrealDBMetricsConfig {
    pub basic_metrics_enabled: bool,
    pub operation_latency_buckets: Vec<f64>,
    pub query_metrics_enabled: bool,
    pub query_latency_buckets: Vec<f64>,
    pub query_complexity_enabled: bool,
    pub query_cache_enabled: bool,
    pub query_cache_buckets: Vec<f64>,
    pub storage_metrics_enabled: bool,
    pub storage_space_enabled: bool,
    pub storage_buckets: Vec<f64>,
    pub memory_metrics_enabled: bool,
    pub memory_pressure_enabled: bool,
    pub gc_metrics_enabled: bool,
    pub memory_buckets: Vec<f64>,
    pub transaction_metrics_enabled: bool,
    pub transaction_latency_buckets: Vec<f64>,
    pub rollback_metrics_enabled: bool,
    pub lock_contention_enabled: bool,
    pub lock_buckets: Vec<f64>,
    pub rag_metrics_enabled: bool,
    pub rag_latency_buckets: Vec<f64>,
    pub embedding_metrics_enabled: bool,
    pub vector_search_enabled: bool,
    pub document_processing_enabled: bool,
    pub embedding_latency_buckets: Vec<f64>,
    pub vector_search_buckets: Vec<f64>,
    pub document_processing_buckets: Vec<f64>,
    pub document_metrics_enabled: bool,
    pub document_count_enabled: bool,
    pub index_metrics_enabled: bool,
    pub index_size_enabled: bool,
    pub index_operation_buckets: Vec<f64>,
}

/// SurrealDB alerts configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SurrealDBAlertsConfig {
    pub enabled: bool,
    pub log_levels: Vec<String>,
    pub alert_on_slow_queries: bool,
    pub alert_on_transaction_issues: bool,
    pub alert_on_authentication_issues: bool,
    pub alert_on_performance_warnings: bool,
    pub thresholds: SurrealDBAlertThresholds,
}

/// SurrealDB alert thresholds
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SurrealDBAlertThresholds {
    pub slow_query_ms: u64,
    pub transaction_timeout_ms: u64,
    pub connection_timeout_ms: u64,
    pub memory_usage_percentage: u8,
    pub storage_usage_percentage: u8,
}

/// LLM configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct LLMConfig {
    pub enable_inference_tracing: bool,
    pub enable_lora_tracing: bool,
    pub enable_embedding_tracing: bool,
    pub sampling_rate: f64,
    pub tracing: LLMTracingConfig,
    pub metrics: LLMMetricsConfig,
    pub logging: LLMLoggingConfig,
    pub dashboards: LLMDashboardsConfig,
    pub alerts: LLMAlertsConfig,
}

/// LLM tracing configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct LLMTracingConfig {
    pub model_loading: bool,
    pub inference: bool,
    pub tokenization: bool,
    pub embedding: bool,
    pub lora: bool,
    pub attributes: LLMTracingAttributes,
}

/// LLM tracing attributes
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct LLMTracingAttributes {
    pub service: String,
    pub environment: String,
}

/// LLM metrics configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct LLMMetricsConfig {
    pub model_performance_enabled: bool,
    pub resource_usage_enabled: bool,
    pub lora_metrics_enabled: bool,
    pub embedding_metrics_enabled: bool,
    pub inference_latency_buckets: Vec<f64>,
    pub batch_size_buckets: Vec<f64>,
    pub memory_usage_buckets: Vec<f64>,
    pub tokens_per_second_buckets: Vec<f64>,
}

/// LLM logging configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct LLMLoggingConfig {
    #[serde(with = "level_serde")]
    pub min_level: Level,
    pub stream_name: String,
    pub components: LLMLoggingComponents,
    pub labels: LLMLoggingLabels,
    pub performance: LLMPerformanceThresholds,
}

/// LLM logging components
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct LLMLoggingComponents {
    pub inference: bool,
    pub lora: bool,
    pub embedding: bool,
    pub tokenization: bool,
    pub performance: bool,
    pub resources: bool,
}

/// LLM logging labels
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct LLMLoggingLabels {
    pub service: String,
    pub environment: String,
}

/// LLM performance thresholds
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct LLMPerformanceThresholds {
    pub inference_warning_threshold_ms: u64,
    pub lora_warning_threshold_ms: u64,
    pub embedding_warning_threshold_ms: u64,
    pub tokenization_warning_threshold_ms: u64,
}

/// LLM dashboards configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct LLMDashboardsConfig {
    pub overview_refresh: String,
    pub overview_tags: Vec<String>,
    pub model_performance_refresh: String,
    pub model_performance_tags: Vec<String>,
    pub resource_usage_refresh: String,
    pub resource_usage_tags: Vec<String>,
    pub lora_refresh: String,
    pub lora_tags: Vec<String>,
    pub embedding_refresh: String,
    pub embedding_tags: Vec<String>,
}

/// LLM alerts configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct LLMAlertsConfig {
    pub enabled: bool,
    pub log_levels: Vec<String>,
    pub performance: LLMPerformanceAlerts,
    pub errors: LLMErrorAlerts,
    pub resources: LLMResourceAlerts,
    pub quality: LLMQualityAlerts,
}

/// LLM performance alerts
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct LLMPerformanceAlerts {
    pub enabled: bool,
    pub high_latency_threshold_ms: u64,
    pub low_throughput_threshold_tokens: u64,
    pub resource_exhaustion_percentage: u8,
}

/// LLM error alerts
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct LLMErrorAlerts {
    pub enabled: bool,
    pub alert_on_model_loading: bool,
    pub alert_on_inference_failures: bool,
    pub alert_on_out_of_memory: bool,
    pub max_consecutive_failures: u32,
}

/// LLM resource alerts
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct LLMResourceAlerts {
    pub enabled: bool,
    pub gpu_memory_threshold_percentage: u8,
    pub cpu_utilization_threshold_percentage: u8,
    pub system_memory_threshold_percentage: u8,
    pub monitoring_interval_seconds: u64,
}

/// LLM quality alerts
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct LLMQualityAlerts {
    pub enabled: bool,
    pub token_error_threshold_percentage: u8,
    pub embedding_quality_threshold: f64,
    pub context_window_usage_percentage: u8,
    pub max_invalid_tokens_per_batch: u32,
}

/// System error configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SystemErrorConfig {
    pub enabled: bool,
    pub logging: SystemErrorLoggingConfig,
    pub metrics: SystemErrorMetricsConfig,
    pub alerts: SystemErrorAlertsConfig,
    pub tracing: SystemErrorTracingConfig,
    pub dashboards: SystemErrorDashboardsConfig,
}

/// System error logging configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SystemErrorLoggingConfig {
    #[serde(with = "level_serde")]
    pub min_level: Level,
    pub include_stacktrace: bool,
    pub include_metadata: bool,
    pub retention_days: u64,
}

/// System error metrics configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SystemErrorMetricsConfig {
    pub enabled: bool,
    pub error_rate_window_minutes: u64,
    pub max_stored_errors: u64,
    pub histogram_buckets: Vec<f64>,
}

/// System error alerts configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SystemErrorAlertsConfig {
    pub enabled: bool,
    pub alert_on_critical: bool,
    pub alert_on_error: bool,
    pub alert_on_warning: bool,
    pub error_rate_threshold: f64,
    pub error_burst_threshold: u32,
    pub error_burst_window_seconds: u64,
}

/// System error tracing configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SystemErrorTracingConfig {
    pub enabled: bool,
    pub include_context: bool,
    pub include_stack_trace: bool,
    pub propagate_correlation_id: bool,
    pub sampling_rate: f64,
    pub error_group_window_seconds: u64,
    pub circuit_breaker_events: bool,
    pub retry_events: bool,
    pub timeout_events: bool,
}

/// System error dashboards configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SystemErrorDashboardsConfig {
    pub enabled: bool,
    pub refresh_interval: String,
    pub error_dashboard_tags: Vec<String>,
    pub circuit_breakers_dashboard_tags: Vec<String>,
    pub retries_dashboard_tags: Vec<String>,
    pub error_distribution_dashboard_tags: Vec<String>,
    pub error_timeline_dashboard_tags: Vec<String>,
    pub error_severity_distribution_refresh: String,
    pub error_source_distribution_refresh: String,
    pub error_rate_timeline_refresh: String,
    pub circuit_breaker_status_refresh: String,
    pub retry_count_refresh: String,
    pub service_health_refresh: String,
    pub error_counts_by_service_refresh: String,
    pub error_counts_by_type_refresh: String,
    pub top_errors_refresh: String,
}

// ============================================================================
// Default Implementations
// ============================================================================

impl Default for Settings {
    fn default() -> Self {
        Self {
            global: GlobalConfig::default(),
            database: DatabaseConfig::default(),
            redis: RedisConfig::default(),
            rabbitmq: RabbitMQConfig::default(),
            telemetry: TelemetryConfig::default(),
        }
    }
}

impl DatabaseConfig {
    /// Get database connection string
    pub fn connection_string(&self) -> String {
        if self.use_http {
            format!("http://{}:{}", self.host, self.port)
        } else {
            format!("ws://{}:{}", self.host, self.port)
        }
    }
}

impl Default for RedisConfig {
    fn default() -> Self {
        Self {
            host: "localhost".to_string(),
            port: 6379,
            password: "".to_string(),
            database: 0,
            connection_timeout_ms: 5000,
            command_timeout_ms: 3000,
            max_connections: 10,
            version: "7.4.1".to_string(),
            cache: RedisCacheConfig::default(),
            performance: RedisPerformanceConfig::default(),
        }
    }
}

impl Default for RedisCacheConfig {
    fn default() -> Self {
        Self {
            default_ttl_seconds: 3600,
            max_memory_policy: "allkeys-lru".to_string(),
            max_memory: "256mb".to_string(),
        }
    }
}

impl Default for RedisPerformanceConfig {
    fn default() -> Self {
        Self {
            enable_pipelining: true,
            pipeline_batch_size: 100,
            enable_compression: true,
            compression_threshold_bytes: 1024,
        }
    }
}

impl RedisConfig {
    /// Get Redis connection URL
    pub fn connection_url(&self) -> String {
        if self.password.is_empty() {
            format!("redis://{}:{}/{}", self.host, self.port, self.database)
        } else {
            format!("redis://:{}@{}:{}/{}", self.password, self.host, self.port, self.database)
        }
    }
}

impl Default for DatabaseConfig {
    fn default() -> Self {
        Self {
            host: "localhost".to_string(),
            port: 8000,
            username: "root".to_string(),
            password: "root".to_string(),
            namespace: "prisma".to_string(),
            database: "prisma".to_string(),
            use_http: true,
            version: "2.2.1".to_string(),
        }
    }
}

impl Default for GlobalEndpoints {
    fn default() -> Self {
        Self {
            prometheus_enabled: true,
            prometheus: "http://*************:30158".to_string(),
            grafana_enabled: true,
            grafana: "http://*************:30455".to_string(),
            grafana_version: Some("11.4.0".to_string()),
            loki_enabled: true,
            loki: "http://*************:32278/loki/api/v1/push".to_string(),
            loki_version: Some("3.3.0".to_string()),
        }
    }
}

impl Default for GlobalConfig {
    fn default() -> Self {
        Self {
            service_name: "prisma-pipeline".to_string(),
            environment: "development".to_string(),
            service_id: "sa-1-prisma-pipeline".to_string(),
            endpoints: GlobalEndpoints::default(),
            api_keys: GlobalApiKeys::default(),
            alerts: GlobalAlerts::default(),
        }
    }
}

impl Default for GlobalApiKeys {
    fn default() -> Self {
        Self {
            prisma_ai_enabled: true,
            slack_enabled: true,
            slack_token: "*********************************************************".to_string(),
            slack_channel: "C08J945BFD2".to_string(),
            discord_enabled: true,
            discord_webhook: "https://discord.com/api/webhooks/1351394594624897107/49-3vXkwo2eoS_j_Wk9EwWr7vBZL-_pvWk8rvIy850TumXPetQhz8yEZ-7PQCmpei0ki".to_string(),
            gmail_enabled: true,
            gmail_email: "<EMAIL>".to_string(),
            gmail_app_password: "ufuq nuok mgjs hnam".to_string(),
        }
    }
}

impl Default for GlobalAlerts {
    fn default() -> Self {
        Self {
            enabled: true,
            default_log_level: "INFO".to_string(),
            enabled_modules: vec!["rabbitmq".to_string(), "llm".to_string(), "storage".to_string()],
            slack: AlertSlackConfig::default(),
            discord: AlertDiscordConfig::default(),
            email: AlertEmailConfig::default(),
        }
    }
}

impl Default for AlertSlackConfig {
    fn default() -> Self {
        Self {
            enabled: true,
            log_levels: vec!["ERROR".to_string(), "WARN".to_string(), "INFO".to_string(), "DEBUG".to_string(), "TRACE".to_string()],
            default_level: "INFO".to_string(),
            modules: vec!["rabbitmq".to_string(), "llm".to_string(), "storage".to_string()],
            message_template: ":bell: *{level}* in module *{module}*:\n>{message}".to_string(),
        }
    }
}

impl Default for AlertDiscordConfig {
    fn default() -> Self {
        Self {
            enabled: true,
            log_levels: vec!["ERROR".to_string(), "WARN".to_string(), "INFO".to_string(), "DEBUG".to_string(), "TRACE".to_string()],
            default_level: "INFO".to_string(),
            modules: vec!["rabbitmq".to_string(), "llm".to_string(), "storage".to_string()],
            message_template: "**{level}** in module **{module}**:\n> {message}".to_string(),
        }
    }
}

impl Default for AlertEmailConfig {
    fn default() -> Self {
        Self {
            enabled: true,
            log_levels: vec!["ERROR".to_string(), "WARN".to_string(), "INFO".to_string(), "DEBUG".to_string(), "TRACE".to_string()],
            default_level: "INFO".to_string(),
            modules: vec!["rabbitmq".to_string(), "llm".to_string(), "storage".to_string()],
            subject_template: "Prisma AI Alert: {level} in {module}".to_string(),
            body_template: "Level: {level}\nModule: {module}\nTimestamp: {timestamp}\n\nMessage:\n{message}".to_string(),
        }
    }
}

impl Default for EnvironmentSettings {
    fn default() -> Self {
        Self {
            service_name: "prisma-pipeline".to_string(),
            environment: "development".to_string(),
            service_id: "sa-1-prisma-pipeline".to_string(),
        }
    }
}

impl Default for TelemetryConfig {
    fn default() -> Self {
        Self {
            endpoints: GlobalEndpoints::default(),
            env_settings: EnvironmentSettings::default(),
            grafana_service_token: "glsa_Go16KlrwTtqtNmeymEg56hIysMVEHyVg_8be567e3".to_string(),
            rabbitmq: None, // RabbitMQ is optional for basic telemetry
            surrealdb: SurrealDBConfig::default(),
            llm: LLMConfig::default(),
            system_error: SystemErrorConfig::default(),
        }
    }
}

// Additional Default implementations for the remaining structs
impl Default for RabbitMQMetricsConfig {
    fn default() -> Self {
        Self {
            connection_metrics_enabled: true,
            connection_latency_buckets: vec![0.001, 0.005, 0.01, 0.05, 0.1, 0.5, 1.0, 5.0],
            tls_metrics_enabled: true,
            channel_metrics_enabled: true,
            channel_latency_buckets: vec![0.001, 0.005, 0.01, 0.05, 0.1, 0.5, 1.0],
            message_metrics_enabled: true,
            message_size_buckets: vec![1024.0, 4096.0, 16384.0, 65536.0, 262144.0, 1048576.0],
            message_processing_buckets: vec![0.001, 0.005, 0.01, 0.05, 0.1, 0.5, 1.0],
            queue_metrics_enabled: true,
            queue_size_enabled: true,
            queue_memory_enabled: true,
            queue_consumer_enabled: true,
            queue_growth_rate_enabled: true,
            exchange_metrics_enabled: true,
            exchange_binding_enabled: true,
            routing_metrics_enabled: true,
            consumer_metrics_enabled: true,
            consumer_latency_buckets: vec![0.001, 0.005, 0.01, 0.05, 0.1, 0.5, 1.0],
            consumer_utilization_enabled: true,
            consumer_lag_enabled: true,
        }
    }
}

impl Default for RabbitMQTelemetryConfig {
    fn default() -> Self {
        Self {
            connection_name: "prisma_alerts".to_string(),
            enable_connection_tracing: true,
            enable_channel_tracing: true,
            enable_queue_tracing: true,
            enable_exchange_tracing: true,
            enable_consumer_tracing: true,
            enable_publisher_tracing: true,
            sampling_rate: 1.0,
            metrics: RabbitMQMetricsConfig::default(),
            logging: RabbitMQLoggingConfig::default(),
        }
    }
}

impl Default for RabbitMQAlertsConfig {
    fn default() -> Self {
        Self {
            enabled: true,
            log_levels: vec!["ERROR".to_string(), "WARN".to_string()],
            alert_on_connection_issues: true,
            alert_on_channel_issues: true,
            alert_on_queue_issues: true,
            alert_on_consumer_issues: true,
            alert_on_publisher_issues: true,
            thresholds: RabbitMQAlertThresholds::default(),
        }
    }
}

impl Default for RabbitMQAlertThresholds {
    fn default() -> Self {
        Self {
            connection_timeout_ms: 5000,
            queue_full_percentage: 90,
            message_rate_threshold: 1000,
            consumer_lag_threshold: 100,
        }
    }
}

impl Default for RabbitMQDashboardsConfig {
    fn default() -> Self {
        Self {
            overview_refresh: "30s".to_string(),
            overview_tags: vec!["rabbitmq".to_string(), "overview".to_string()],
            connection_refresh: "10s".to_string(),
            connection_tags: vec!["rabbitmq".to_string(), "connection".to_string()],
            performance_refresh: "5s".to_string(),
            performance_tags: vec!["rabbitmq".to_string(), "performance".to_string()],
            queue_exchange_refresh: "15s".to_string(),
            queue_exchange_tags: vec!["rabbitmq".to_string(), "queues".to_string()],
            error_refresh: "5s".to_string(),
            error_tags: vec!["rabbitmq".to_string(), "errors".to_string()],
        }
    }
}

impl Default for RabbitMQLoggingConfig {
    fn default() -> Self {
        Self {
            min_level: Level::INFO,
            stream_name: "rabbitmq_logs".to_string(),
            components: RabbitMQLoggingComponents::default(),
            labels: RabbitMQLoggingLabels::default(),
            performance: RabbitMQPerformanceThresholds::default(),
        }
    }
}

impl Default for RabbitMQLoggingComponents {
    fn default() -> Self {
        Self {
            connection: true,
            channel: true,
            queue: true,
            exchange: true,
            publish: true,
            consume: true,
            routing: true,
            performance: true,
        }
    }
}

impl Default for RabbitMQLoggingLabels {
    fn default() -> Self {
        Self {
            service: "prisma-pipeline".to_string(),
            environment: "development".to_string(),
        }
    }
}

impl Default for RabbitMQPerformanceThresholds {
    fn default() -> Self {
        Self {
            connection_warning_threshold_ms: 1000,
            channel_warning_threshold_ms: 500,
            queue_warning_threshold_ms: 100,
            publish_warning_threshold_ms: 50,
            consume_warning_threshold_ms: 100,
        }
    }
}

impl Default for RabbitMQConfig {
    fn default() -> Self {
        Self {
            host: "localhost".to_string(),
            port: 5672,
            username: "guest".to_string(),
            password: "guest".to_string(),
            vhost: "/".to_string(),
            connection_name: "prisma_alerts".to_string(),
            metrics: RabbitMQMetricsConfig::default(),
            telemetry: RabbitMQTelemetryConfig::default(),
            alerts: RabbitMQAlertsConfig::default(),
            dashboards: RabbitMQDashboardsConfig::default(),
        }
    }
}

impl Default for SurrealDBLoggingConfig {
    fn default() -> Self {
        Self {
            min_level: Level::INFO,
            stream_name: "surrealdb_logs".to_string(),
            components: SurrealDBLoggingComponents::default(),
            labels: SurrealDBLoggingLabels::default(),
            query: SurrealDBQueryLogging::default(),
            transaction: SurrealDBTransactionLogging::default(),
            document: SurrealDBDocumentLogging::default(),
            index: SurrealDBIndexLogging::default(),
            rag: SurrealDBRagLogging::default(),
            performance: SurrealDBPerformanceThresholds::default(),
            loki: SurrealDBLokiConfig::default(),
        }
    }
}

impl Default for SurrealDBLoggingComponents {
    fn default() -> Self {
        Self {
            query: true,
            transaction: true,
            document: true,
            index: true,
            authentication: true,
            performance: true,
            rag: true,
        }
    }
}

impl Default for SurrealDBLoggingLabels {
    fn default() -> Self {
        Self {
            service: "prisma-pipeline".to_string(),
            environment: "development".to_string(),
        }
    }
}

impl Default for SurrealDBQueryLogging {
    fn default() -> Self {
        Self {
            log_slow_queries: true,
            slow_query_threshold_ms: 1000,
            log_query_plans: false,
            log_query_cache_events: true,
        }
    }
}

impl Default for SurrealDBTransactionLogging {
    fn default() -> Self {
        Self {
            log_begin_commit: true,
            log_rollbacks: true,
            log_deadlocks: true,
            log_lock_wait_time: true,
        }
    }
}

impl Default for SurrealDBDocumentLogging {
    fn default() -> Self {
        Self {
            log_mutations: true,
            log_batch_operations: true,
            log_validation_errors: true,
        }
    }
}

impl Default for SurrealDBIndexLogging {
    fn default() -> Self {
        Self {
            log_builds: true,
            log_updates: true,
            log_analysis: false,
        }
    }
}

impl Default for SurrealDBRagLogging {
    fn default() -> Self {
        Self {
            log_embeddings: true,
            log_vector_searches: true,
            log_document_processing: true,
        }
    }
}

impl Default for SurrealDBPerformanceThresholds {
    fn default() -> Self {
        Self {
            query_warning_threshold_ms: 1000,
            transaction_warning_threshold_ms: 5000,
            index_warning_threshold_ms: 2000,
            embedding_warning_threshold_ms: 3000,
            vector_search_warning_threshold_ms: 1500,
            document_processing_warning_threshold_ms: 2000,
        }
    }
}

impl Default for SurrealDBLokiConfig {
    fn default() -> Self {
        Self {
            endpoint: "http://*************:32278/loki/api/v1/push".to_string(),
            batch_size: 100,
            batch_wait_ms: 1000,
            retention_days: 30,
            labels: SurrealDBLokiLabels::default(),
        }
    }
}

impl Default for SurrealDBLokiLabels {
    fn default() -> Self {
        Self {
            app: "prisma-ai".to_string(),
            component: "surrealdb".to_string(),
        }
    }
}

impl Default for SurrealDBConfig {
    fn default() -> Self {
        Self {
            connection_name: "prisma_ai_surrealdb".to_string(),
            enable_query_tracing: true,
            enable_transaction_tracing: true,
            enable_document_tracing: true,
            enable_index_tracing: true,
            sampling_rate: 1.0,
            logging: SurrealDBLoggingConfig::default(),
            metrics: SurrealDBMetricsConfig::default(),
            alerts: SurrealDBAlertsConfig::default(),
        }
    }
}

impl Default for LLMTracingConfig {
    fn default() -> Self {
        Self {
            model_loading: true,
            inference: true,
            tokenization: true,
            embedding: true,
            lora: true,
            attributes: LLMTracingAttributes::default(),
        }
    }
}

impl Default for LLMTracingAttributes {
    fn default() -> Self {
        Self {
            service: "prisma-pipeline".to_string(),
            environment: "development".to_string(),
        }
    }
}

impl Default for LLMMetricsConfig {
    fn default() -> Self {
        Self {
            model_performance_enabled: true,
            resource_usage_enabled: true,
            lora_metrics_enabled: true,
            embedding_metrics_enabled: true,
            inference_latency_buckets: vec![0.1, 0.5, 1.0, 5.0, 10.0, 30.0, 60.0],
            batch_size_buckets: vec![1.0, 4.0, 8.0, 16.0, 32.0, 64.0, 128.0],
            memory_usage_buckets: vec![1073741824.0, 2147483648.0, 4294967296.0, 8589934592.0],
            tokens_per_second_buckets: vec![1.0, 5.0, 10.0, 50.0, 100.0, 500.0, 1000.0],
        }
    }
}

impl Default for LLMLoggingConfig {
    fn default() -> Self {
        Self {
            min_level: Level::INFO,
            stream_name: "llm_logs".to_string(),
            components: LLMLoggingComponents::default(),
            labels: LLMLoggingLabels::default(),
            performance: LLMPerformanceThresholds::default(),
        }
    }
}

impl Default for LLMLoggingComponents {
    fn default() -> Self {
        Self {
            inference: true,
            lora: true,
            embedding: true,
            tokenization: true,
            performance: true,
            resources: true,
        }
    }
}

impl Default for LLMLoggingLabels {
    fn default() -> Self {
        Self {
            service: "prisma-pipeline".to_string(),
            environment: "development".to_string(),
        }
    }
}

impl Default for LLMPerformanceThresholds {
    fn default() -> Self {
        Self {
            inference_warning_threshold_ms: 5000,
            lora_warning_threshold_ms: 10000,
            embedding_warning_threshold_ms: 3000,
            tokenization_warning_threshold_ms: 1000,
        }
    }
}

impl Default for LLMDashboardsConfig {
    fn default() -> Self {
        Self {
            overview_refresh: "30s".to_string(),
            overview_tags: vec!["llm".to_string(), "overview".to_string()],
            model_performance_refresh: "10s".to_string(),
            model_performance_tags: vec!["llm".to_string(), "performance".to_string()],
            resource_usage_refresh: "5s".to_string(),
            resource_usage_tags: vec!["llm".to_string(), "resources".to_string()],
            lora_refresh: "15s".to_string(),
            lora_tags: vec!["llm".to_string(), "lora".to_string()],
            embedding_refresh: "20s".to_string(),
            embedding_tags: vec!["llm".to_string(), "embedding".to_string()],
        }
    }
}

impl Default for LLMAlertsConfig {
    fn default() -> Self {
        Self {
            enabled: true,
            log_levels: vec!["ERROR".to_string(), "WARN".to_string()],
            performance: LLMPerformanceAlerts::default(),
            errors: LLMErrorAlerts::default(),
            resources: LLMResourceAlerts::default(),
            quality: LLMQualityAlerts::default(),
        }
    }
}

impl Default for LLMPerformanceAlerts {
    fn default() -> Self {
        Self {
            enabled: true,
            high_latency_threshold_ms: 10000,
            low_throughput_threshold_tokens: 10,
            resource_exhaustion_percentage: 90,
        }
    }
}

impl Default for LLMErrorAlerts {
    fn default() -> Self {
        Self {
            enabled: true,
            alert_on_model_loading: true,
            alert_on_inference_failures: true,
            alert_on_out_of_memory: true,
            max_consecutive_failures: 5,
        }
    }
}

impl Default for LLMResourceAlerts {
    fn default() -> Self {
        Self {
            enabled: true,
            gpu_memory_threshold_percentage: 85,
            cpu_utilization_threshold_percentage: 90,
            system_memory_threshold_percentage: 85,
            monitoring_interval_seconds: 30,
        }
    }
}

impl Default for LLMQualityAlerts {
    fn default() -> Self {
        Self {
            enabled: true,
            token_error_threshold_percentage: 5,
            embedding_quality_threshold: 0.8,
            context_window_usage_percentage: 95,
            max_invalid_tokens_per_batch: 10,
        }
    }
}

impl Default for LLMConfig {
    fn default() -> Self {
        Self {
            enable_inference_tracing: true,
            enable_lora_tracing: true,
            enable_embedding_tracing: true,
            sampling_rate: 1.0,
            tracing: LLMTracingConfig::default(),
            metrics: LLMMetricsConfig::default(),
            logging: LLMLoggingConfig::default(),
            dashboards: LLMDashboardsConfig::default(),
            alerts: LLMAlertsConfig::default(),
        }
    }
}

impl Default for SystemErrorLoggingConfig {
    fn default() -> Self {
        Self {
            min_level: Level::ERROR,
            include_stacktrace: true,
            include_metadata: true,
            retention_days: 30,
        }
    }
}

impl Default for SystemErrorMetricsConfig {
    fn default() -> Self {
        Self {
            enabled: true,
            error_rate_window_minutes: 5,
            max_stored_errors: 1000,
            histogram_buckets: vec![0.001, 0.005, 0.01, 0.05, 0.1, 0.5, 1.0, 5.0],
        }
    }
}

impl Default for SystemErrorAlertsConfig {
    fn default() -> Self {
        Self {
            enabled: true,
            alert_on_critical: true,
            alert_on_error: true,
            alert_on_warning: false,
            error_rate_threshold: 0.1,
            error_burst_threshold: 10,
            error_burst_window_seconds: 60,
        }
    }
}

impl Default for SystemErrorTracingConfig {
    fn default() -> Self {
        Self {
            enabled: true,
            include_context: true,
            include_stack_trace: true,
            propagate_correlation_id: true,
            sampling_rate: 1.0,
            error_group_window_seconds: 300,
            circuit_breaker_events: true,
            retry_events: true,
            timeout_events: true,
        }
    }
}

impl Default for SystemErrorDashboardsConfig {
    fn default() -> Self {
        Self {
            enabled: true,
            refresh_interval: "30s".to_string(),
            error_dashboard_tags: vec!["errors".to_string(), "system".to_string()],
            circuit_breakers_dashboard_tags: vec!["circuit-breaker".to_string(), "resilience".to_string()],
            retries_dashboard_tags: vec!["retries".to_string(), "resilience".to_string()],
            error_distribution_dashboard_tags: vec!["errors".to_string(), "distribution".to_string()],
            error_timeline_dashboard_tags: vec!["errors".to_string(), "timeline".to_string()],
            error_severity_distribution_refresh: "1m".to_string(),
            error_source_distribution_refresh: "1m".to_string(),
            error_rate_timeline_refresh: "10s".to_string(),
            circuit_breaker_status_refresh: "5s".to_string(),
            retry_count_refresh: "10s".to_string(),
            service_health_refresh: "30s".to_string(),
            error_counts_by_service_refresh: "1m".to_string(),
            error_counts_by_type_refresh: "1m".to_string(),
            top_errors_refresh: "2m".to_string(),
        }
    }
}

impl Default for SystemErrorConfig {
    fn default() -> Self {
        Self {
            enabled: true,
            logging: SystemErrorLoggingConfig::default(),
            metrics: SystemErrorMetricsConfig::default(),
            alerts: SystemErrorAlertsConfig::default(),
            tracing: SystemErrorTracingConfig::default(),
            dashboards: SystemErrorDashboardsConfig::default(),
        }
    }
}

impl Default for SurrealDBMetricsConfig {
    fn default() -> Self {
        Self {
            basic_metrics_enabled: true,
            operation_latency_buckets: vec![0.001, 0.005, 0.01, 0.05, 0.1, 0.5, 1.0, 5.0],
            query_metrics_enabled: true,
            query_latency_buckets: vec![0.01, 0.05, 0.1, 0.5, 1.0, 5.0, 10.0],
            query_complexity_enabled: true,
            query_cache_enabled: true,
            query_cache_buckets: vec![0.001, 0.005, 0.01, 0.05, 0.1],
            storage_metrics_enabled: true,
            storage_space_enabled: true,
            storage_buckets: vec![1024.0, 4096.0, 16384.0, 65536.0, 262144.0, 1048576.0],
            memory_metrics_enabled: true,
            memory_pressure_enabled: true,
            gc_metrics_enabled: true,
            memory_buckets: vec![1048576.0, 4194304.0, 16777216.0, 67108864.0, 268435456.0],
            transaction_metrics_enabled: true,
            transaction_latency_buckets: vec![0.01, 0.05, 0.1, 0.5, 1.0, 5.0],
            rollback_metrics_enabled: true,
            lock_contention_enabled: true,
            lock_buckets: vec![0.001, 0.005, 0.01, 0.05, 0.1, 0.5],
            rag_metrics_enabled: true,
            rag_latency_buckets: vec![0.1, 0.5, 1.0, 5.0, 10.0, 30.0],
            embedding_metrics_enabled: true,
            vector_search_enabled: true,
            document_processing_enabled: true,
            embedding_latency_buckets: vec![0.1, 0.5, 1.0, 5.0, 10.0],
            vector_search_buckets: vec![0.01, 0.05, 0.1, 0.5, 1.0, 5.0],
            document_processing_buckets: vec![0.1, 0.5, 1.0, 5.0, 10.0],
            document_metrics_enabled: true,
            document_count_enabled: true,
            index_metrics_enabled: true,
            index_size_enabled: true,
            index_operation_buckets: vec![0.01, 0.05, 0.1, 0.5, 1.0, 5.0],
        }
    }
}

impl Default for SurrealDBAlertsConfig {
    fn default() -> Self {
        Self {
            enabled: true,
            log_levels: vec!["ERROR".to_string(), "WARN".to_string()],
            alert_on_slow_queries: true,
            alert_on_transaction_issues: true,
            alert_on_authentication_issues: true,
            alert_on_performance_warnings: true,
            thresholds: SurrealDBAlertThresholds::default(),
        }
    }
}

impl Default for SurrealDBAlertThresholds {
    fn default() -> Self {
        Self {
            slow_query_ms: 1000,
            transaction_timeout_ms: 30000,
            connection_timeout_ms: 5000,
            memory_usage_percentage: 85,
            storage_usage_percentage: 90,
        }
    }
}
