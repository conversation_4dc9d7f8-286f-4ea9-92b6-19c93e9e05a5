use serde::{Deserialize, Serialize};
use std::fmt;
use std::error::Error as StdError;
use thiserror::Error;
use std::collections::HashMap;
use chrono;
use std::fmt::{<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, Result as FmtR<PERSON><PERSON>};
use std::error::Error;

/// Service status enum for tracking health of components
#[derive(Debug, Clone, Copy, PartialEq, Eq)]
pub enum ServiceStatus {
    /// Service is functioning normally
    Healthy,
    /// Service is experiencing issues but still operational
    Degraded,
    /// Service is not operational
    Failed,
}

/// Core error types for the application
#[derive(Debug, Clone)]
pub enum PrismaError {
    /// Database-related errors
    DatabaseError(String),
    /// Connection errors (network, timeouts, etc.)
    ConnectionError(String),
    /// Authentication or authorization errors
    AuthError(String),
    /// Configuration errors
    ConfigError(String),
    /// Resource not found
    NotFoundError(String),
    /// Input validation errors
    ValidationError(String),
    /// Serialization/deserialization errors
    SerializationError(String),
    /// Cache-related errors
    CacheError(String),
    /// System-level errors
    SystemError(String),
    /// I/O errors
    IO(String),
    /// Large Language Model errors
    LLM(String),
    /// Generic error with message
    Generic(String),
    /// Unknown error
    Unknown(String),
    /// Circuit breaker is open
    CircuitBreakerOpen {
        /// Name of the service that is open
        service: String,
    },
    /// Retry limit exceeded
    RetryLimitExceeded {
        /// Name of the operation that failed
        operation: String,
        /// Number of attempts made
        attempts: u32,
    },
    /// Chat session already exists
    SessionAlreadyExists(String),
    /// Chat participant already exists
    ParticipantAlreadyExists(String),
    /// Chat session not found
    SessionNotFound(String),
    /// Chat participant not found
    ParticipantNotFound(String),
    /// Serialization error
    Serialization(String),
}

impl Display for PrismaError {
    fn fmt(&self, f: &mut Formatter<'_>) -> FmtResult {
        match self {
            Self::DatabaseError(msg) => write!(f, "Database error: {}", msg),
            Self::ConnectionError(msg) => write!(f, "Connection error: {}", msg),
            Self::AuthError(msg) => write!(f, "Authentication error: {}", msg),
            Self::ConfigError(msg) => write!(f, "Configuration error: {}", msg),
            Self::NotFoundError(msg) => write!(f, "Not found: {}", msg),
            Self::ValidationError(msg) => write!(f, "Validation error: {}", msg),
            Self::SerializationError(msg) => write!(f, "Serialization error: {}", msg),
            Self::CacheError(msg) => write!(f, "Cache error: {}", msg),
            Self::SystemError(msg) => write!(f, "System error: {}", msg),
            Self::IO(msg) => write!(f, "I/O error: {}", msg),
            Self::LLM(msg) => write!(f, "LLM error: {}", msg),
            Self::Generic(msg) => write!(f, "Error: {}", msg),
            Self::Unknown(msg) => write!(f, "Unknown error: {}", msg),
            Self::CircuitBreakerOpen { service } => {
                write!(f, "Circuit breaker open for service: {}", service)
            }
            Self::RetryLimitExceeded { operation, attempts } => {
                write!(f, "Retry limit exceeded for operation '{}' after {} attempts", operation, attempts)
            }
            Self::SessionAlreadyExists(msg) => write!(f, "Session already exists: {}", msg),
            Self::ParticipantAlreadyExists(msg) => write!(f, "Participant already exists: {}", msg),
            Self::SessionNotFound(msg) => write!(f, "Session not found: {}", msg),
            Self::ParticipantNotFound(msg) => write!(f, "Participant not found: {}", msg),
            Self::Serialization(msg) => write!(f, "Serialization error: {}", msg),
        }
    }
}

impl Error for PrismaError {}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum ErrorSeverity {
    Critical,
    Error,
    Warning,
    Info,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum ErrorSource {
    Database,
    RabbitMQ,
    LLM,
    Authentication,
    Authorization,
    Configuration,
    CircuitBreaker,
    Retry,
    Timeout,
    Validation,
    Other,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ErrorContext {
    pub severity: ErrorSeverity,
    pub source: ErrorSource,
    pub timestamp: chrono::DateTime<chrono::Utc>,
    pub correlation_id: Option<String>,
    pub metadata: HashMap<String, String>,
}

// Result type alias
pub type PrismaResult<T> = Result<T, PrismaError>;

impl PrismaError {
    pub fn severity(&self) -> ErrorSeverity {
        match self {
            PrismaError::CircuitBreakerOpen { .. } => ErrorSeverity::Critical,
            PrismaError::DatabaseError(_) => ErrorSeverity::Error,
            PrismaError::ConnectionError(_) => ErrorSeverity::Error,
            PrismaError::AuthError(_) => ErrorSeverity::Error,
            PrismaError::ConfigError(_) => ErrorSeverity::Error,
            PrismaError::NotFoundError(_) => ErrorSeverity::Error,
            PrismaError::ValidationError(_) => ErrorSeverity::Warning,
            PrismaError::SerializationError(_) => ErrorSeverity::Error,
            PrismaError::CacheError(_) => ErrorSeverity::Error,
            PrismaError::SystemError(_) => ErrorSeverity::Error,
            PrismaError::IO(_) => ErrorSeverity::Error,
            PrismaError::LLM(_) => ErrorSeverity::Error,
            PrismaError::Generic(_) => ErrorSeverity::Error,
            PrismaError::Unknown(_) => ErrorSeverity::Error,
            PrismaError::RetryLimitExceeded { .. } => ErrorSeverity::Error,
            PrismaError::SessionAlreadyExists(_) => ErrorSeverity::Warning,
            PrismaError::ParticipantAlreadyExists(_) => ErrorSeverity::Warning,
            PrismaError::SessionNotFound(_) => ErrorSeverity::Error,
            PrismaError::ParticipantNotFound(_) => ErrorSeverity::Error,
            PrismaError::Serialization(_) => ErrorSeverity::Error,
        }
    }

    pub fn source(&self) -> ErrorSource {
        match self {
            PrismaError::DatabaseError(_) => ErrorSource::Database,
            PrismaError::ConnectionError(_) => ErrorSource::Other,
            PrismaError::AuthError(_) => ErrorSource::Authentication,
            PrismaError::ConfigError(_) => ErrorSource::Other,
            PrismaError::NotFoundError(_) => ErrorSource::Other,
            PrismaError::ValidationError(_) => ErrorSource::Validation,
            PrismaError::SerializationError(_) => ErrorSource::Other,
            PrismaError::CacheError(_) => ErrorSource::Other,
            PrismaError::SystemError(_) => ErrorSource::Other,
            PrismaError::IO(_) => ErrorSource::Other,
            PrismaError::LLM(_) => ErrorSource::LLM,
            PrismaError::Generic(_) => ErrorSource::Other,
            PrismaError::Unknown(_) => ErrorSource::Other,
            PrismaError::CircuitBreakerOpen { .. } => ErrorSource::CircuitBreaker,
            PrismaError::RetryLimitExceeded { .. } => ErrorSource::Retry,
            PrismaError::SessionAlreadyExists(_) => ErrorSource::Validation,
            PrismaError::ParticipantAlreadyExists(_) => ErrorSource::Validation,
            PrismaError::SessionNotFound(_) => ErrorSource::Other,
            PrismaError::ParticipantNotFound(_) => ErrorSource::Other,
            PrismaError::Serialization(_) => ErrorSource::Other,
        }
    }
}

#[derive(Debug, Clone, Error)]
pub enum SystemMonitorError {
    #[error("System monitoring error: {0}")]
    MonitoringError(String),

    #[error("Not time to update the monitor")]
    NotTimeToUpdate,

    #[error("Invalid resource data type")]
    InvalidResourceData,

    #[error("Resource not available: {0}")]
    ResourceNotAvailable(String),

    #[error("Monitoring operation failed: {0}")]
    OperationFailed(String),
}

impl From<SystemMonitorError> for PrismaError {
    fn from(err: SystemMonitorError) -> Self {
        match err {
            SystemMonitorError::MonitoringError(msg) => PrismaError::SystemError(msg),
            SystemMonitorError::NotTimeToUpdate => PrismaError::Generic("Not time to update monitor".to_string()),
            SystemMonitorError::InvalidResourceData => PrismaError::SystemError("Invalid resource data type".to_string()),
            SystemMonitorError::ResourceNotAvailable(msg) => PrismaError::SystemError(format!("Resource not available: {}", msg)),
            SystemMonitorError::OperationFailed(msg) => PrismaError::SystemError(format!("Monitoring operation failed: {}", msg)),
        }
    }
}

